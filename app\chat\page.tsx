import { createClient } from '@/lib/supabase/server'
import { getCurrentUser, getUserProfile } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { ProtectedLayout } from '@/components/layout'
import { RouteGuard } from '@/components/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  MessageSquare, 
  Bot,
  Send,
  Mic,
  Paperclip,
  MoreVertical,
  Sparkles,
  FileText,
  Clock,
  Users
} from 'lucide-react'

export default async function ChatPage() {
  const { user, error } = await getCurrentUser()
  
  if (error || !user) {
    redirect('/auth/login')
  }

  const { data: profile } = await getUserProfile(user.id)
  
  if (!profile) {
    redirect('/auth/setup-profile')
  }

  return (
    <ProtectedLayout requireAuth={true} allowedRoles={['ciudadano', 'admin', 'super_admin']}>
      <RouteGuard requiredRoles={['ciudadano', 'admin', 'super_admin']}>
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <Bot className="h-6 w-6 mr-2 text-chia-blue-600" />
                  Asistente Virtual IA
                </h1>
                <p className="text-gray-600">
                  Obtén ayuda instantánea sobre trámites y servicios municipales
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  En línea
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Chat Interface */}
            <div className="lg:col-span-3">
              <Card className="h-[600px] flex flex-col">
                <CardHeader className="border-b">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-chia-blue-600 rounded-full flex items-center justify-center">
                        <Bot className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">Asistente Municipal</CardTitle>
                        <CardDescription>
                          Especializado en trámites de Chía
                        </CardDescription>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>

                {/* Chat Messages */}
                <CardContent className="flex-1 overflow-y-auto p-0">
                  <div className="p-6 space-y-4">
                    {/* Welcome Message */}
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 bg-chia-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <Bot className="h-4 w-4 text-white" />
                      </div>
                      <div className="bg-gray-100 rounded-lg p-3 max-w-md">
                        <p className="text-sm">
                          ¡Hola {profile.full_name}! 👋 Soy tu asistente virtual del Municipio de Chía. 
                          Puedo ayudarte con información sobre trámites, requisitos, costos y mucho más.
                        </p>
                        <p className="text-xs text-gray-500 mt-2">
                          Hace un momento
                        </p>
                      </div>
                    </div>

                    {/* Suggested Questions */}
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 bg-chia-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <Sparkles className="h-4 w-4 text-white" />
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm text-gray-600">Preguntas frecuentes:</p>
                        <div className="space-y-2">
                          <Button variant="outline" size="sm" className="text-left justify-start h-auto p-2">
                            ¿Cómo puedo obtener mi certificado de residencia?
                          </Button>
                          <Button variant="outline" size="sm" className="text-left justify-start h-auto p-2">
                            ¿Cuáles son los requisitos para el permiso de construcción?
                          </Button>
                          <Button variant="outline" size="sm" className="text-left justify-start h-auto p-2">
                            ¿Dónde puedo pagar el impuesto predial?
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>

                {/* Chat Input */}
                <div className="border-t p-4">
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm">
                      <Paperclip className="h-4 w-4" />
                    </Button>
                    <div className="flex-1 relative">
                      <input
                        type="text"
                        placeholder="Escribe tu pregunta aquí..."
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-chia-blue-500 focus:border-transparent"
                      />
                    </div>
                    <Button variant="ghost" size="sm">
                      <Mic className="h-4 w-4" />
                    </Button>
                    <Button size="sm">
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    El asistente puede cometer errores. Verifica información importante.
                  </p>
                </div>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Acciones Rápidas</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button variant="outline" className="w-full justify-start">
                    <FileText className="h-4 w-4 mr-2" />
                    Consultar Trámite
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Clock className="h-4 w-4 mr-2" />
                    Horarios de Atención
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Users className="h-4 w-4 mr-2" />
                    Contactar Funcionario
                  </Button>
                </CardContent>
              </Card>

              {/* Recent Topics */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Temas Recientes</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="text-sm">
                      <p className="font-medium text-gray-900">Certificado de Residencia</p>
                      <p className="text-gray-500 text-xs">Hace 2 días</p>
                    </div>
                    <div className="text-sm">
                      <p className="font-medium text-gray-900">Impuesto Predial</p>
                      <p className="text-gray-500 text-xs">Hace 1 semana</p>
                    </div>
                    <div className="text-sm">
                      <p className="font-medium text-gray-900">Licencia de Construcción</p>
                      <p className="text-gray-500 text-xs">Hace 2 semanas</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Help & Support */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Ayuda y Soporte</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">Línea de Atención</p>
                    <p className="text-gray-600">(601) 123-4567</p>
                  </div>
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">Email</p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">Horarios</p>
                    <p className="text-gray-600">Lun - Vie: 8:00 AM - 5:00 PM</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </RouteGuard>
    </ProtectedLayout>
  )
}
