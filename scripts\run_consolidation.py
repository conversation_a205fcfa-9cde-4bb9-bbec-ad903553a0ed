#!/usr/bin/env python3
"""
=============================================
EJECUTOR DE CONSOLIDACIÓN JSON - SISTEMA MUNICIPAL CHÍA
=============================================
Script de ejecución simplificado para el proceso completo de consolidación
Fecha: 2025-01-06
Autor: Sistema Municipal Chía - Consolidation Runner
=============================================
"""

import subprocess
import sys
import time
from pathlib import Path
from datetime import datetime

def print_header():
    """Imprimir encabezado del proceso"""
    print("🚀 CONSOLIDACIÓN JSON - SISTEMA MUNICIPAL DE CHÍA")
    print("=" * 60)
    print(f"📅 Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Directorio: {Path.cwd()}")
    print("=" * 60)

def print_step(step_num: int, title: str, description: str = ""):
    """Imprimir paso del proceso"""
    print(f"\n{step_num}️⃣ {title}")
    if description:
        print(f"   {description}")
    print("-" * 40)

def run_command(command: list, description: str) -> bool:
    """Ejecutar comando y manejar resultado"""
    print(f"🔄 Ejecutando: {description}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ {description} completado ({duration:.1f}s)")
            
            # Mostrar output relevante
            if result.stdout:
                # Filtrar líneas importantes del output
                lines = result.stdout.split('\n')
                important_lines = [
                    line for line in lines 
                    if any(keyword in line for keyword in ['✓', '✅', '📊', '💾', '🎉', 'INFO'])
                ]
                
                if important_lines:
                    print("📋 Resumen:")
                    for line in important_lines[-5:]:  # Últimas 5 líneas importantes
                        if line.strip():
                            print(f"   {line.strip()}")
            
            return True
        else:
            print(f"❌ {description} falló ({duration:.1f}s)")
            print(f"🚨 Código de error: {result.returncode}")
            
            if result.stderr:
                print("📝 Error details:")
                error_lines = result.stderr.split('\n')
                for line in error_lines[:10]:  # Primeras 10 líneas de error
                    if line.strip():
                        print(f"   {line.strip()}")
            
            return False
            
    except Exception as e:
        print(f"❌ Error ejecutando {description}: {e}")
        return False

def check_prerequisites() -> bool:
    """Verificar prerrequisitos del sistema"""
    print_step(0, "VERIFICACIÓN DE PRERREQUISITOS", "Comprobando dependencias y configuración")
    
    scripts_dir = Path(__file__).parent
    
    # Verificar archivos necesarios
    required_files = [
        "generate_consolidated_json.py",
        "validate_consolidated_json.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not (scripts_dir / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Archivos faltantes: {', '.join(missing_files)}")
        return False
    
    # Verificar Python y dependencias
    try:
        import psycopg2
        print("✅ psycopg2 disponible")
    except ImportError:
        print("❌ psycopg2 no está instalado")
        print("💡 Instalar con: pip install psycopg2-binary")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ python-dotenv disponible")
    except ImportError:
        print("⚠️ python-dotenv no está instalado (opcional)")
    
    # Verificar configuración de entorno
    import os
    if not os.getenv('SUPABASE_DB_PASSWORD'):
        env_file = Path('.env')
        if env_file.exists():
            print("✅ Archivo .env encontrado")
        else:
            print("⚠️ Variables de entorno no configuradas")
            print("💡 Ejecutar: python scripts/setup_env_consolidation.py")
    else:
        print("✅ Variables de entorno configuradas")
    
    print("✅ Prerrequisitos verificados")
    return True

def generate_json() -> tuple[bool, Path]:
    """Generar JSON consolidado"""
    print_step(1, "GENERACIÓN DE JSON CONSOLIDADO", "Extrayendo datos de la base de datos")
    
    scripts_dir = Path(__file__).parent
    generator_script = scripts_dir / "generate_consolidated_json.py"
    
    success = run_command(
        [sys.executable, str(generator_script)],
        "Generación de JSON consolidado"
    )
    
    # Verificar archivo generado
    json_file = scripts_dir / "sistema_municipal_chia_consolidado.json"
    
    if success and json_file.exists():
        file_size = json_file.stat().st_size / (1024 * 1024)
        print(f"📁 Archivo generado: {json_file.name} ({file_size:.2f} MB)")
        return True, json_file
    else:
        print("❌ No se pudo generar el archivo JSON")
        return False, None

def validate_json(json_file: Path) -> bool:
    """Validar JSON generado"""
    print_step(2, "VALIDACIÓN DEL JSON", "Verificando estructura e integridad de datos")
    
    scripts_dir = Path(__file__).parent
    validator_script = scripts_dir / "validate_consolidated_json.py"
    
    success = run_command(
        [sys.executable, str(validator_script), str(json_file)],
        "Validación del JSON consolidado"
    )
    
    # Buscar archivos de reporte generados
    report_files = list(scripts_dir.glob("validation_report_*.md"))
    if report_files:
        latest_report = max(report_files, key=lambda x: x.stat().st_mtime)
        print(f"📋 Reporte de validación: {latest_report.name}")
    
    # Buscar archivo optimizado
    optimized_file = scripts_dir / f"{json_file.stem}_optimized.json"
    if optimized_file.exists():
        original_size = json_file.stat().st_size / (1024 * 1024)
        optimized_size = optimized_file.stat().st_size / (1024 * 1024)
        reduction = ((original_size - optimized_size) / original_size) * 100
        print(f"⚡ Versión optimizada: {optimized_file.name}")
        print(f"📉 Reducción de tamaño: {reduction:.1f}% ({original_size:.2f} MB → {optimized_size:.2f} MB)")
    
    return success

def generate_final_summary(json_file: Path, validation_success: bool):
    """Generar resumen final del proceso"""
    print_step(3, "RESUMEN FINAL", "Consolidación completada")
    
    scripts_dir = Path(__file__).parent
    
    # Información del archivo principal
    if json_file and json_file.exists():
        file_size = json_file.stat().st_size / (1024 * 1024)
        print(f"📁 Archivo principal: {json_file.name} ({file_size:.2f} MB)")
    
    # Archivos adicionales generados
    additional_files = []
    
    # Archivo optimizado
    optimized_file = scripts_dir / f"{json_file.stem}_optimized.json"
    if optimized_file.exists():
        additional_files.append(f"⚡ {optimized_file.name}")
    
    # Resumen de consolidación
    summary_file = scripts_dir / "consolidation_summary.md"
    if summary_file.exists():
        additional_files.append(f"📊 {summary_file.name}")
    
    # Reportes de validación
    report_files = list(scripts_dir.glob("validation_report_*.md"))
    if report_files:
        latest_report = max(report_files, key=lambda x: x.stat().st_mtime)
        additional_files.append(f"📋 {latest_report.name}")
    
    if additional_files:
        print("\n📂 Archivos adicionales generados:")
        for file in additional_files:
            print(f"   {file}")
    
    # Estado final
    print(f"\n🎯 Estado final:")
    if validation_success:
        print("   ✅ Consolidación exitosa")
        print("   ✅ Validación aprobada")
        print("   🎉 JSON listo para uso en producción")
    else:
        print("   ⚠️ Consolidación completada con advertencias")
        print("   📋 Revisar reporte de validación para detalles")
    
    # Próximos pasos
    print(f"\n🚀 Próximos pasos:")
    print("   1. Revisar archivos generados en scripts/")
    print("   2. Integrar JSON en APIs del sistema")
    print("   3. Configurar actualización periódica")
    print("   4. Implementar monitoreo de cambios")

def main():
    """Función principal"""
    print_header()
    
    try:
        # 0. Verificar prerrequisitos
        if not check_prerequisites():
            print("\n❌ Prerrequisitos no cumplidos - abortando proceso")
            sys.exit(1)
        
        # 1. Generar JSON consolidado
        generation_success, json_file = generate_json()
        if not generation_success:
            print("\n❌ Error en la generación - abortando proceso")
            sys.exit(1)
        
        # 2. Validar JSON generado
        validation_success = validate_json(json_file)
        
        # 3. Generar resumen final
        generate_final_summary(json_file, validation_success)
        
        # Resultado final
        if validation_success:
            print(f"\n🎉 PROCESO COMPLETADO EXITOSAMENTE")
            print(f"⏱️ Tiempo total: {time.time() - start_time:.1f} segundos")
            sys.exit(0)
        else:
            print(f"\n⚠️ PROCESO COMPLETADO CON ADVERTENCIAS")
            print(f"📋 Revisar reportes para más información")
            sys.exit(0)  # No es un error crítico
            
    except KeyboardInterrupt:
        print(f"\n\n⏹️ Proceso interrumpido por el usuario")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Error inesperado: {e}")
        sys.exit(1)

if __name__ == "__main__":
    start_time = time.time()
    main()
