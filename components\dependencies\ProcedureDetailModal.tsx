'use client'

import React from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  FileText,
  BarChart3,
  Clock,
  DollarSign,
  ExternalLink,
  Download,
  CheckCircle,
  AlertCircle,
  Building2,
  User,
  Calendar,
  MapPin
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ProcedureDetailModalProps {
  procedure: any | null
  isOpen: boolean
  onClose: () => void
}

export function ProcedureDetailModal({
  procedure,
  isOpen,
  onClose
}: ProcedureDetailModalProps) {
  if (!procedure) return null

  const isTramite = procedure.Nombre !== undefined
  const isOPA = procedure.OPA !== undefined

  const getTitle = () => {
    if (isTramite) return procedure.Nombre
    if (isOPA) return procedure.OPA
    return 'Procedimiento'
  }

  const getType = () => {
    if (isTramite) return 'TRAMITE'
    if (isOPA) return 'OPA'
    return 'PROCEDIMIENTO'
  }

  const getTypeLabel = () => {
    if (isTramite) return 'Trámite'
    if (isOPA) return 'Otra Prestación Administrativa'
    return 'Procedimiento'
  }

  const getTypeColor = () => {
    if (isTramite) return 'bg-chia-green-100 text-chia-green-700 border-chia-green-200'
    if (isOPA) return 'bg-chia-blue-100 text-chia-blue-700 border-chia-blue-200'
    return 'bg-gray-100 text-gray-700 border-gray-200'
  }

  const getIcon = () => {
    if (isTramite) return <FileText className="h-6 w-6" />
    if (isOPA) return <BarChart3 className="h-6 w-6" />
    return <FileText className="h-6 w-6" />
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden bg-white/95 backdrop-blur-md border-0 shadow-2xl rounded-3xl">
        <DialogHeader className="pb-6">
          <div className="flex items-start space-x-4">
            <div className={cn(
              "w-16 h-16 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg",
              isTramite ? 'bg-gradient-to-br from-chia-green-100 to-chia-green-200 text-chia-green-600' : 'bg-gradient-to-br from-chia-blue-100 to-chia-blue-200 text-chia-blue-600'
            )}>
              {getIcon()}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3 mb-3">
                <Badge variant="outline" className={cn("text-sm font-semibold", getTypeColor())}>
                  {getTypeLabel()}
                </Badge>
                {procedure.Formulario === 'Sí' && (
                  <Badge variant="outline" className="text-sm font-semibold bg-orange-100 text-orange-700 border-orange-200">
                    Requiere Formulario
                  </Badge>
                )}
              </div>
              <DialogTitle className="text-2xl font-bold text-gray-900 leading-tight mb-2">
                {getTitle()}
              </DialogTitle>
              <DialogDescription className="text-lg">
                {isTramite && procedure.dependencia && (
                  <span className="flex items-center text-gray-600 font-medium">
                    <Building2 className="h-5 w-5 mr-2" />
                    {procedure.dependencia}
                    {procedure.subdependencia && ` - ${procedure.subdependencia}`}
                  </span>
                )}
                {isOPA && (
                  <span className="flex items-center text-gray-600 font-medium">
                    <Building2 className="h-5 w-5 mr-2" />
                    {procedure.dependencia}
                    {procedure.subdependencia && ` - ${procedure.subdependencia}`}
                  </span>
                )}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <ScrollArea className="h-[60vh] mt-4">
          <div className="space-y-6">
            {/* Información básica */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Costo */}
              <Card>
                <CardContent className="p-4 text-center">
                  <DollarSign className={cn(
                    "h-8 w-8 mx-auto mb-2",
                    (isTramite && procedure['¿Tiene pago?'] === 'No') || isOPA 
                      ? "text-green-600" 
                      : "text-orange-600"
                  )} />
                  <div className="text-lg font-bold">
                    {isTramite 
                      ? (procedure['¿Tiene pago?'] === 'No' ? 'Gratuito' : procedure['¿Tiene pago?'] || 'No especificado')
                      : 'Gratuito'
                    }
                  </div>
                  <div className="text-sm text-gray-600">Costo</div>
                </CardContent>
              </Card>

              {/* Tiempo de respuesta */}
              <Card>
                <CardContent className="p-4 text-center">
                  <Clock className="h-8 w-8 text-primary mx-auto mb-2" />
                  <div className="text-lg font-bold">
                    {isTramite 
                      ? (procedure['Tiempo de respuesta'] || 'No especificado')
                      : '1 día hábil'
                    }
                  </div>
                  <div className="text-sm text-gray-600">Tiempo de respuesta</div>
                </CardContent>
              </Card>

              {/* Estado */}
              <Card>
                <CardContent className="p-4 text-center">
                  <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <div className="text-lg font-bold">Activo</div>
                  <div className="text-sm text-gray-600">Estado</div>
                </CardContent>
              </Card>
            </div>

            {/* Descripción detallada */}
            {isTramite && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="h-5 w-5" />
                    <span>Descripción del Trámite</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 leading-relaxed">
                    {procedure.Descripcion || 
                     `El trámite "${procedure.Nombre}" es un procedimiento administrativo que permite a los ciudadanos acceder a servicios específicos de la ${procedure.dependencia}. Este trámite está diseñado para facilitar la gestión de solicitudes de manera eficiente y transparente.`}
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Requisitos */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5" />
                  <span>Requisitos y Documentos</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isTramite && procedure.Requisitos ? (
                  <div className="space-y-2">
                    {procedure.Requisitos.split('\n').map((req: string, index: number) => (
                      <div key={index} className="flex items-start space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{req.trim()}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex items-start space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                      <span className="text-gray-700">Documento de identidad vigente</span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                      <span className="text-gray-700">Formulario de solicitud (si aplica)</span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                      <span className="text-gray-700">Documentos adicionales según el caso específico</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Proceso paso a paso */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5" />
                  <span>Proceso Paso a Paso</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-primary/10 text-primary rounded-full flex items-center justify-center text-sm font-bold">
                      1
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Preparar documentación</h4>
                      <p className="text-gray-600 text-sm">Reúne todos los documentos requeridos según la lista de requisitos.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-primary/10 text-primary rounded-full flex items-center justify-center text-sm font-bold">
                      2
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Presentar solicitud</h4>
                      <p className="text-gray-600 text-sm">Acércate a la dependencia correspondiente o utiliza los canales digitales disponibles.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-primary/10 text-primary rounded-full flex items-center justify-center text-sm font-bold">
                      3
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Seguimiento</h4>
                      <p className="text-gray-600 text-sm">Realiza seguimiento al estado de tu solicitud a través de los canales oficiales.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-bold">
                      4
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Recibir respuesta</h4>
                      <p className="text-gray-600 text-sm">Recibe la respuesta en el tiempo estimado según el tipo de procedimiento.</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Información de contacto */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MapPin className="h-5 w-5" />
                  <span>¿Dónde realizar este trámite?</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Building2 className="h-4 w-4 text-primary" />
                    <span className="font-medium">{procedure.dependencia || 'Alcaldía Municipal de Chía'}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4 text-primary" />
                    <span className="text-gray-600">Carrera 11 No. 17-25, Chía, Cundinamarca</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-primary" />
                    <span className="text-gray-600">Lunes a Viernes: 8:00 AM - 5:00 PM</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Acciones */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              {isTramite && procedure['Visualización trámite en el SUIT'] && (
                <Button className="flex-1 bg-primary hover:bg-primary/90">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Ver en SUIT
                </Button>
              )}
              
              {isTramite && procedure['Visualización trámite en GOV.CO'] && (
                <Button variant="outline" className="flex-1">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Ver en GOV.CO
                </Button>
              )}
              
              {procedure.Formulario === 'Sí' && (
                <Button variant="outline" className="flex-1">
                  <Download className="h-4 w-4 mr-2" />
                  Descargar Formulario
                </Button>
              )}
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
