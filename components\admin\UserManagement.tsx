'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { 
  Search, 
  Filter, 
  UserPlus, 
  Edit, 
  Trash2, 
  MoreHorizontal,
  Shield,
  Building,
  Mail,
  Phone,
  Calendar
} from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { useAuth, useRole, usePermissions } from '@/hooks'
import { 
  PermissionButton,
  <PERSON><PERSON>or<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Conditional<PERSON>ontent
} from '@/components/auth'

interface User {
  id: string
  email: string
  full_name: string
  document_number: string
  phone: string
  created_at: string
  role: {
    id: string
    name: string
    display_name: string
  } | null
  dependency: {
    id: string
    name: string
  } | null
}

export function UserManagement() {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('all')
  const [dependencyFilter, setDependencyFilter] = useState('all')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [dependencies, setDependencies] = useState<any[]>([])
  const [roles, setRoles] = useState<any[]>([])

  const { profile } = useAuth()
  const { isSuperAdmin, isAdmin } = useRole()
  const { canEditUser, canDeleteUser, canManageAllUsers } = usePermissions()

  const supabase = createClient()

  useEffect(() => {
    fetchUsers()
    fetchDependencies()
    fetchRoles()
  }, [])

  const fetchUsers = async () => {
    try {
      setLoading(true)
      let query = supabase
        .from('profiles')
        .select(`
          id,
          email,
          full_name,
          document_number,
          phone,
          created_at,
          role:roles(id, name, display_name),
          dependency:dependencies(id, name)
        `)

      // Filter based on user role
      if (isAdmin && profile?.dependency_id) {
        // Admin can only see users from their dependency
        query = query.eq('dependency_id', profile.dependency_id)
      }

      const { data, error } = await query.order('created_at', { ascending: false })

      if (error) throw error
      setUsers(data || [])
    } catch (error) {
      console.error('Error fetching users:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchDependencies = async () => {
    try {
      const { data, error } = await supabase
        .from('dependencies')
        .select('id, name')
        .order('name')

      if (error) throw error
      setDependencies(data || [])
    } catch (error) {
      console.error('Error fetching dependencies:', error)
    }
  }

  const fetchRoles = async () => {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('id, name, display_name')
        .order('name')

      if (error) throw error
      setRoles(data || [])
    } catch (error) {
      console.error('Error fetching roles:', error)
    }
  }

  const filteredUsers = users.filter(user => {
    const matchesSearch = 
      user.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.document_number?.includes(searchTerm)

    const matchesRole = roleFilter === 'all' || user.role?.name === roleFilter
    const matchesDependency = dependencyFilter === 'all' || user.dependency?.id === dependencyFilter

    return matchesSearch && matchesRole && matchesDependency
  })

  const handleEditUser = (user: User) => {
    setSelectedUser(user)
    setIsEditDialogOpen(true)
  }

  const handleUpdateUser = async (updatedData: any) => {
    if (!selectedUser) return

    try {
      const { error } = await supabase
        .from('profiles')
        .update(updatedData)
        .eq('id', selectedUser.id)

      if (error) throw error

      await fetchUsers()
      setIsEditDialogOpen(false)
      setSelectedUser(null)
    } catch (error) {
      console.error('Error updating user:', error)
    }
  }

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar este usuario?')) return

    try {
      const { error } = await supabase
        .from('profiles')
        .delete()
        .eq('id', userId)

      if (error) throw error
      await fetchUsers()
    } catch (error) {
      console.error('Error deleting user:', error)
    }
  }

  const getRoleBadgeVariant = (roleName: string) => {
    switch (roleName) {
      case 'super_admin': return 'destructive'
      case 'admin': return 'default'
      case 'ciudadano': return 'secondary'
      default: return 'outline'
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Gestión de Usuarios</CardTitle>
              <CardDescription>
                <ConditionalContent
                  adminContent={`Administra usuarios de ${profile?.dependency?.name || 'tu dependencia'}`}
                  superAdminContent="Administra todos los usuarios del sistema"
                >
                  Gestiona los usuarios del sistema
                </ConditionalContent>
              </CardDescription>
            </div>
            <PermissionButton
              requiredPermissions={['canCreateUsers']}
              variant="default"
            >
              <UserPlus className="h-4 w-4 mr-2" />
              Nuevo Usuario
            </PermissionButton>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <Label htmlFor="search">Buscar</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Buscar por nombre, email o documento..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="w-full sm:w-48">
              <Label htmlFor="role-filter">Rol</Label>
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filtrar por rol" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los roles</SelectItem>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.name}>
                      {role.display_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <ShowForSuperAdmin>
              <div className="w-full sm:w-48">
                <Label htmlFor="dependency-filter">Dependencia</Label>
                <Select value={dependencyFilter} onValueChange={setDependencyFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filtrar por dependencia" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todas las dependencias</SelectItem>
                    {dependencies.map((dependency) => (
                      <SelectItem key={dependency.id} value={dependency.id}>
                        {dependency.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </ShowForSuperAdmin>
          </div>

          {/* Users Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Usuario</TableHead>
                  <TableHead>Contacto</TableHead>
                  <TableHead>Rol</TableHead>
                  <ShowForSuperAdmin>
                    <TableHead>Dependencia</TableHead>
                  </ShowForSuperAdmin>
                  <TableHead>Registro</TableHead>
                  <TableHead className="text-right">Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      Cargando usuarios...
                    </TableCell>
                  </TableRow>
                ) : filteredUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      No se encontraron usuarios
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{user.full_name}</div>
                          <div className="text-sm text-gray-500">
                            CC: {user.document_number}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center text-sm">
                            <Mail className="h-3 w-3 mr-1" />
                            {user.email}
                          </div>
                          {user.phone && (
                            <div className="flex items-center text-sm text-gray-500">
                              <Phone className="h-3 w-3 mr-1" />
                              {user.phone}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getRoleBadgeVariant(user.role?.name || '')}>
                          <Shield className="h-3 w-3 mr-1" />
                          {user.role?.display_name || 'Sin rol'}
                        </Badge>
                      </TableCell>
                      <ShowForSuperAdmin>
                        <TableCell>
                          {user.dependency ? (
                            <div className="flex items-center text-sm">
                              <Building className="h-3 w-3 mr-1" />
                              {user.dependency.name}
                            </div>
                          ) : (
                            <span className="text-gray-400">Sin dependencia</span>
                          )}
                        </TableCell>
                      </ShowForSuperAdmin>
                      <TableCell>
                        <div className="flex items-center text-sm text-gray-500">
                          <Calendar className="h-3 w-3 mr-1" />
                          {new Date(user.created_at).toLocaleDateString('es-CO')}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <PermissionButton
                            requiredPermissions={['canEditUser']}
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditUser(user)}
                            resourceId={user.id}
                            resourceDependencyId={user.dependency?.id}
                          >
                            <Edit className="h-3 w-3" />
                          </PermissionButton>
                          <PermissionButton
                            requiredPermissions={['canDeleteUser']}
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteUser(user.id)}
                            resourceId={user.id}
                            resourceDependencyId={user.dependency?.id}
                          >
                            <Trash2 className="h-3 w-3" />
                          </PermissionButton>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Editar Usuario</DialogTitle>
            <DialogDescription>
              Modifica la información del usuario seleccionado.
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <UserEditForm
              user={selectedUser}
              roles={roles}
              dependencies={dependencies}
              onSave={handleUpdateUser}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

// User Edit Form Component
interface UserEditFormProps {
  user: User
  roles: any[]
  dependencies: any[]
  onSave: (data: any) => void
  onCancel: () => void
}

function UserEditForm({ user, roles, dependencies, onSave, onCancel }: UserEditFormProps) {
  const [formData, setFormData] = useState({
    full_name: user.full_name || '',
    phone: user.phone || '',
    role_id: user.role?.id || '',
    dependency_id: user.dependency?.id || ''
  })

  const { isSuperAdmin } = useRole()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave(formData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="full_name">Nombre Completo</Label>
        <Input
          id="full_name"
          value={formData.full_name}
          onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
          required
        />
      </div>

      <div>
        <Label htmlFor="phone">Teléfono</Label>
        <Input
          id="phone"
          value={formData.phone}
          onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
        />
      </div>

      <div>
        <Label htmlFor="role">Rol</Label>
        <Select value={formData.role_id} onValueChange={(value) => setFormData({ ...formData, role_id: value })}>
          <SelectTrigger>
            <SelectValue placeholder="Seleccionar rol" />
          </SelectTrigger>
          <SelectContent>
            {roles.map((role) => (
              <SelectItem key={role.id} value={role.id}>
                {role.display_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <ShowForSuperAdmin>
        <div>
          <Label htmlFor="dependency">Dependencia</Label>
          <Select value={formData.dependency_id} onValueChange={(value) => setFormData({ ...formData, dependency_id: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Seleccionar dependencia" />
            </SelectTrigger>
            <SelectContent>
              {dependencies.map((dependency) => (
                <SelectItem key={dependency.id} value={dependency.id}>
                  {dependency.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </ShowForSuperAdmin>

      <DialogFooter>
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancelar
        </Button>
        <Button type="submit">
          Guardar Cambios
        </Button>
      </DialogFooter>
    </form>
  )
}
