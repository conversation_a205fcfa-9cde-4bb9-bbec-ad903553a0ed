'use client'

import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/hooks/useAuth'
import { useRole } from '@/hooks/useRole'
import { Shield, User, Crown, Building } from 'lucide-react'

interface UserRoleDisplayProps {
  showIcon?: boolean
  showDependency?: boolean
  variant?: 'default' | 'secondary' | 'destructive' | 'outline'
  size?: 'sm' | 'default' | 'lg'
}

export function UserRoleDisplay({
  showIcon = true,
  showDependency = false,
  variant = 'default',
  size = 'default',
}: UserRoleDisplayProps) {
  const { profile } = useAuth()
  const { roleName, isCitizen, isAdmin, isSuperAdmin } = useRole()

  if (!profile || !roleName) {
    return null
  }

  const getRoleConfig = () => {
    switch (roleName) {
      case 'ciudadano':
        return {
          label: 'Ciudadano',
          icon: User,
          color: 'bg-blue-100 text-blue-800 border-blue-200',
        }
      case 'admin':
        return {
          label: 'Administrador',
          icon: Shield,
          color: 'bg-orange-100 text-orange-800 border-orange-200',
        }
      case 'super_admin':
        return {
          label: 'Super Administrador',
          icon: Crown,
          color: 'bg-purple-100 text-purple-800 border-purple-200',
        }
      default:
        return {
          label: 'Sin rol',
          icon: User,
          color: 'bg-gray-100 text-gray-800 border-gray-200',
        }
    }
  }

  const { label, icon: Icon, color } = getRoleConfig()

  return (
    <div className="flex items-center gap-2">
      <Badge
        variant={variant}
        className={`${color} ${
          size === 'sm' ? 'text-xs px-2 py-1' : 
          size === 'lg' ? 'text-sm px-3 py-1.5' : 
          'text-xs px-2.5 py-1'
        }`}
      >
        {showIcon && <Icon className="h-3 w-3 mr-1" />}
        {label}
      </Badge>
      
      {showDependency && profile.dependency_id && (
        <Badge variant="outline" className="text-xs">
          <Building className="h-3 w-3 mr-1" />
          {/* This would need to be populated with actual dependency name */}
          Dependencia
        </Badge>
      )}
    </div>
  )
}

// Detailed role information component
export function UserRoleInfo() {
  const { profile } = useAuth()
  const { roleName, permissions } = useRole()

  if (!profile || !roleName) {
    return null
  }

  const getPermissionSummary = () => {
    const activePermissions = Object.entries(permissions)
      .filter(([_, value]) => value === true)
      .map(([key, _]) => key)

    return activePermissions.length
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-gray-700">Rol actual:</span>
        <UserRoleDisplay showIcon={true} />
      </div>
      
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-gray-700">Permisos activos:</span>
        <Badge variant="outline" className="text-xs">
          {getPermissionSummary()} permisos
        </Badge>
      </div>
      
      {profile.dependency_id && (
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">Dependencia:</span>
          <Badge variant="outline" className="text-xs">
            <Building className="h-3 w-3 mr-1" />
            {profile.dependency_id}
          </Badge>
        </div>
      )}
    </div>
  )
}
