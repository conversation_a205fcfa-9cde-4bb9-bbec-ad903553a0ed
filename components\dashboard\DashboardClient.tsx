'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Settings, 
  Users, 
  BarChart3, 
  Shield, 
  Plus,
  FileText,
  MessageSquare,
  Building,
  ArrowRight,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'

// Import our new auth hooks and components
import { useAuth, useRole, usePermissions } from '@/hooks'
import { 
  RoleGuard, 
  ShowForAdmin, 
  ShowForSuperAdmin, 
  ShowForCitizen,
  PermissionButton,
  AdminButton,
  SuperAdminButton,
  CitizenButton,
  UserRoleDisplay,
  ConditionalContent,
  ShowWithPermission
} from '@/components/auth'

export function DashboardClient() {
  const { user, profile } = useAuth()
  const { isCitizen, isAdmin, isSuperAdmin, roleName, permissions } = useRole()
  const permissionChecks = usePermissions()

  return (
    <div className="space-y-6">
      {/* Role-based Welcome Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl">
                <ConditionalContent
                  citizenContent="Portal Ciudadano"
                  adminContent="Panel de Administración"
                  superAdminContent="Panel de Super Administrador"
                >
                  Dashboard
                </ConditionalContent>
              </CardTitle>
              <CardDescription>
                Rol actual: <UserRoleDisplay showIcon={true} />
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <ShowForCitizen>
                <Badge variant="outline" className="text-blue-600">
                  Ciudadano
                </Badge>
              </ShowForCitizen>
              <ShowForAdmin>
                <Badge variant="outline" className="text-orange-600">
                  Administrador
                </Badge>
              </ShowForAdmin>
              <ShowForSuperAdmin>
                <Badge variant="outline" className="text-purple-600">
                  Super Admin
                </Badge>
              </ShowForSuperAdmin>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-sm">
              <strong>Usuario:</strong> {profile?.full_name || 'N/A'}
            </div>
            <div className="text-sm">
              <strong>Email:</strong> {user?.email || 'N/A'}
            </div>
            <div className="text-sm">
              <strong>Dependencia:</strong> {profile?.dependency_id || 'N/A'}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Role-based Action Buttons */}
      <Card>
        <CardHeader>
          <CardTitle>Acciones Disponibles</CardTitle>
          <CardDescription>
            Botones habilitados según tu rol y permisos
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Citizen Actions */}
            <CitizenButton className="w-full justify-start">
              <FileText className="h-4 w-4 mr-2" />
              Crear Trámite
            </CitizenButton>

            <PermissionButton
              requiredPermissions={['canChatWithAI']}
              className="w-full justify-start"
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              Chat con IA
            </PermissionButton>

            {/* Admin Actions */}
            <AdminButton className="w-full justify-start">
              <Users className="h-4 w-4 mr-2" />
              Gestionar Usuarios
            </AdminButton>

            <PermissionButton
              requiredPermissions={['canViewDependencyReports']}
              className="w-full justify-start"
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              Ver Reportes
            </PermissionButton>

            {/* Super Admin Actions */}
            <SuperAdminButton className="w-full justify-start">
              <Settings className="h-4 w-4 mr-2" />
              Configuración Sistema
            </SuperAdminButton>

            <PermissionButton
              requiredPermissions={['canManageDependencies']}
              className="w-full justify-start"
            >
              <Building className="h-4 w-4 mr-2" />
              Gestionar Dependencias
            </PermissionButton>
          </div>
        </CardContent>
      </Card>

      {/* Conditional Content Based on Roles */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Citizen Content */}
        <RoleGuard roles={['ciudadano']}>
          <Card>
            <CardHeader>
              <CardTitle>Mis Trámites</CardTitle>
              <CardDescription>
                Gestiona tus solicitudes y procedimientos
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button variant="outline" className="w-full justify-start">
                <FileText className="h-4 w-4 mr-2" />
                Ver Mis Trámites
                <ArrowRight className="h-4 w-4 ml-auto" />
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Plus className="h-4 w-4 mr-2" />
                Nuevo Trámite
                <ArrowRight className="h-4 w-4 ml-auto" />
              </Button>
            </CardContent>
          </Card>
        </RoleGuard>

        {/* Admin Content */}
        <ShowForAdmin>
          <Card>
            <CardHeader>
              <CardTitle>Administración</CardTitle>
              <CardDescription>
                Gestiona tu dependencia
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <ShowWithPermission permission="canViewDependencyUsers">
                <Button variant="outline" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-2" />
                  Usuarios de la Dependencia
                  <ArrowRight className="h-4 w-4 ml-auto" />
                </Button>
              </ShowWithPermission>
              
              <ShowWithPermission permission="canViewDependencyProcedures">
                <Button variant="outline" className="w-full justify-start">
                  <FileText className="h-4 w-4 mr-2" />
                  Trámites de la Dependencia
                  <ArrowRight className="h-4 w-4 ml-auto" />
                </Button>
              </ShowWithPermission>
            </CardContent>
          </Card>
        </ShowForAdmin>

        {/* Super Admin Content */}
        <ShowForSuperAdmin>
          <Card>
            <CardHeader>
              <CardTitle>Super Administración</CardTitle>
              <CardDescription>
                Control total del sistema
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button variant="outline" className="w-full justify-start">
                <Shield className="h-4 w-4 mr-2" />
                Gestionar Roles
                <ArrowRight className="h-4 w-4 ml-auto" />
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Building className="h-4 w-4 mr-2" />
                Gestionar Dependencias
                <ArrowRight className="h-4 w-4 ml-auto" />
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <BarChart3 className="h-4 w-4 mr-2" />
                Reportes Globales
                <ArrowRight className="h-4 w-4 ml-auto" />
              </Button>
            </CardContent>
          </Card>
        </ShowForSuperAdmin>
      </div>

      {/* Permissions Demo */}
      <Card>
        <CardHeader>
          <CardTitle>Demo de Permisos</CardTitle>
          <CardDescription>
            Ejemplos de botones con diferentes permisos
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* View Permission */}
            <PermissionButton
              requiredPermissions={['canViewOwnProcedures']}
              variant="outline"
              className="w-full"
              tooltipContent="Permiso para ver tus propios trámites"
            >
              <Eye className="h-4 w-4 mr-2" />
              Ver
            </PermissionButton>

            {/* Edit Permission */}
            <PermissionButton
              requiredPermissions={['canEditProfile']}
              variant="outline"
              className="w-full"
              tooltipContent="Permiso para editar tu perfil"
            >
              <Edit className="h-4 w-4 mr-2" />
              Editar
            </PermissionButton>

            {/* Delete Permission (usually restricted) */}
            <PermissionButton
              requiredPermissions={['canManageAllUsers']}
              variant="destructive"
              className="w-full"
              tooltipContent="Solo super administradores pueden eliminar usuarios"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Eliminar
            </PermissionButton>

            {/* Multiple roles required */}
            <PermissionButton
              requiredRoles={['admin', 'super_admin']}
              variant="outline"
              className="w-full"
              tooltipContent="Requiere rol de administrador o superior"
            >
              <Settings className="h-4 w-4 mr-2" />
              Configurar
            </PermissionButton>

            {/* Hidden when no permission */}
            <PermissionButton
              requiredPermissions={['canManageSystem']}
              hideWhenDisabled={true}
              className="w-full"
            >
              <Shield className="h-4 w-4 mr-2" />
              Sistema (Oculto)
            </PermissionButton>

            {/* Multiple permissions required */}
            <PermissionButton
              requiredPermissions={['canViewAllUsers', 'canManageAllUsers']}
              requireAll={true}
              variant="outline"
              className="w-full"
              tooltipContent="Requiere TODOS los permisos especificados"
            >
              <Users className="h-4 w-4 mr-2" />
              Admin Total
            </PermissionButton>
          </div>
        </CardContent>
      </Card>

      {/* Permissions Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Resumen de Permisos</CardTitle>
          <CardDescription>
            Permisos activos para tu rol actual
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(permissions)
              .filter(([_, value]) => value === true)
              .map(([permission, _]) => (
                <Badge key={permission} variant="outline" className="text-xs">
                  {permission}
                </Badge>
              ))}
          </div>
          {Object.values(permissions).every(p => p === false) && (
            <p className="text-gray-500 text-sm">No hay permisos activos</p>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
