#!/usr/bin/env python3
"""
=============================================
SCRIPT DE EXPORTACIÓN COMPLETA - SISTEMA MUNICIPAL CHÍA
=============================================
Exporta estructura, datos y políticas RLS de las tablas principales
Fecha: 2025-01-06
Autor: Sistema Municipal Chía - DBA Export
Requisitos: psycopg2, python-dotenv
=============================================
"""

import os
import sys
import json
import csv
import subprocess
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging

try:
    import psycopg2
    from psycopg2.extras import RealDictCursor
except ImportError:
    print("Error: psycopg2 no está instalado. Instala con: pip install psycopg2-binary")
    sys.exit(1)

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("Advertencia: python-dotenv no está instalado. Usando variables de entorno del sistema.")

class MunicipalDBExporter:
    """Exportador de base de datos municipal de Chía"""
    
    def __init__(self):
        self.setup_logging()
        self.setup_config()
        self.setup_export_directory()
        
        # Tablas principales a exportar
        self.main_tables = [
            "dependencies",
            "subdependencies", 
            "procedures",
            "opas",
            "faq_themes",
            "municipal_faqs"
        ]
        
    def setup_logging(self):
        """Configurar logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('export_log.txt')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_config(self):
        """Configurar conexión a la base de datos"""
        self.db_config = {
            'host': os.getenv('SUPABASE_DB_HOST', 'db.zeieudvbhlrlnfkwejoh.supabase.co'),
            'port': int(os.getenv('SUPABASE_DB_PORT', '5432')),
            'database': os.getenv('SUPABASE_DB_NAME', 'postgres'),
            'user': os.getenv('SUPABASE_DB_USER', 'postgres'),
            'password': os.getenv('SUPABASE_DB_PASSWORD')
        }
        
        if not self.db_config['password']:
            self.logger.error("SUPABASE_DB_PASSWORD no está configurada")
            print("Por favor configura las variables de entorno de Supabase:")
            print("export SUPABASE_DB_PASSWORD='tu_password'")
            sys.exit(1)
            
    def setup_export_directory(self):
        """Crear directorio de exportación"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.export_dir = Path(f"database_exports/export_{timestamp}")
        self.export_dir.mkdir(parents=True, exist_ok=True)
        self.logger.info(f"Directorio de exportación: {self.export_dir}")
        
    def get_connection(self):
        """Obtener conexión a la base de datos"""
        try:
            conn = psycopg2.connect(**self.db_config)
            return conn
        except psycopg2.Error as e:
            self.logger.error(f"Error conectando a la base de datos: {e}")
            sys.exit(1)
            
    def export_table_structure(self, table_name: str) -> str:
        """Exportar estructura de una tabla"""
        self.logger.info(f"Exportando estructura de tabla: {table_name}")
        
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                # Obtener información de columnas
                cur.execute("""
                    SELECT 
                        column_name,
                        data_type,
                        udt_name,
                        is_nullable,
                        column_default,
                        ordinal_position
                    FROM information_schema.columns 
                    WHERE table_name = %s 
                    ORDER BY ordinal_position
                """, (table_name,))
                
                columns = cur.fetchall()
                
                if not columns:
                    self.logger.warning(f"No se encontraron columnas para la tabla {table_name}")
                    return ""
                
                # Generar DDL
                ddl = f"-- Tabla: {table_name}\n"
                ddl += f"CREATE TABLE {table_name} (\n"
                
                column_definitions = []
                for col in columns:
                    col_def = f"  {col['column_name']} "
                    
                    # Tipo de dato
                    if col['data_type'] == 'USER-DEFINED':
                        col_def += col['udt_name']
                    elif col['data_type'] == 'character varying':
                        col_def += 'TEXT'
                    elif col['data_type'] == 'timestamp with time zone':
                        col_def += 'TIMESTAMP WITH TIME ZONE'
                    else:
                        col_def += col['data_type'].upper()
                    
                    # NOT NULL
                    if col['is_nullable'] == 'NO':
                        col_def += ' NOT NULL'
                    
                    # DEFAULT
                    if col['column_default']:
                        col_def += f" DEFAULT {col['column_default']}"
                    
                    column_definitions.append(col_def)
                
                ddl += ",\n".join(column_definitions)
                ddl += "\n);\n\n"
                
                return ddl
                
    def export_table_data_csv(self, table_name: str):
        """Exportar datos de una tabla en formato CSV"""
        self.logger.info(f"Exportando datos CSV de tabla: {table_name}")
        
        csv_file = self.export_dir / f"data_{table_name}.csv"
        
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                # Obtener nombres de columnas
                cur.execute(f"SELECT * FROM {table_name} LIMIT 0")
                column_names = [desc[0] for desc in cur.description]
                
                # Exportar datos
                cur.execute(f"SELECT * FROM {table_name} ORDER BY created_at")
                
                with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(column_names)
                    
                    for row in cur.fetchall():
                        # Convertir valores especiales a string
                        processed_row = []
                        for value in row:
                            if value is None:
                                processed_row.append('')
                            elif isinstance(value, dict):
                                processed_row.append(json.dumps(value))
                            elif isinstance(value, list):
                                processed_row.append(json.dumps(value))
                            else:
                                processed_row.append(str(value))
                        writer.writerow(processed_row)
                        
        self.logger.info(f"✓ Datos CSV exportados: {csv_file}")
        
    def export_table_data_sql(self, table_name: str):
        """Exportar datos de una tabla en formato SQL INSERT"""
        self.logger.info(f"Exportando datos SQL de tabla: {table_name}")
        
        sql_file = self.export_dir / f"data_{table_name}.sql"
        
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute(f"SELECT * FROM {table_name} ORDER BY created_at")
                rows = cur.fetchall()
                
                if not rows:
                    self.logger.warning(f"No hay datos en la tabla {table_name}")
                    return
                
                with open(sql_file, 'w', encoding='utf-8') as f:
                    f.write(f"-- Datos tabla: {table_name}\n")
                    f.write(f"-- Generado el: {datetime.now()}\n\n")
                    f.write(f"TRUNCATE TABLE {table_name} CASCADE;\n\n")
                    
                    for row in rows:
                        columns = list(row.keys())
                        values = []
                        
                        for value in row.values():
                            if value is None:
                                values.append('NULL')
                            elif isinstance(value, str):
                                values.append(f"'{value.replace(chr(39), chr(39)+chr(39))}'")
                            elif isinstance(value, (dict, list)):
                                values.append(f"'{json.dumps(value).replace(chr(39), chr(39)+chr(39))}'")
                            elif isinstance(value, bool):
                                values.append('true' if value else 'false')
                            else:
                                values.append(str(value))
                        
                        insert_stmt = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({', '.join(values)});\n"
                        f.write(insert_stmt)
                        
        self.logger.info(f"✓ Datos SQL exportados: {sql_file}")
        
    def export_rls_policies(self):
        """Exportar políticas RLS"""
        self.logger.info("Exportando políticas RLS...")
        
        rls_file = self.export_dir / "rls_policies.sql"
        
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT 
                        tablename,
                        policyname,
                        cmd,
                        roles,
                        qual,
                        with_check
                    FROM pg_policies 
                    WHERE tablename = ANY(%s)
                    ORDER BY tablename, policyname
                """, (self.main_tables,))
                
                policies = cur.fetchall()
                
                with open(rls_file, 'w', encoding='utf-8') as f:
                    f.write("-- =============================================\n")
                    f.write("-- POLÍTICAS RLS - SISTEMA MUNICIPAL CHÍA\n")
                    f.write(f"-- Generado el: {datetime.now()}\n")
                    f.write("-- =============================================\n\n")
                    
                    # Funciones auxiliares
                    f.write("-- Funciones auxiliares para RLS\n")
                    f.write("""CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
BEGIN
  RETURN (
    SELECT r.name
    FROM profiles p
    JOIN roles r ON p.role_id = r.id
    WHERE p.id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

""")
                    
                    current_table = None
                    for policy in policies:
                        if current_table != policy['tablename']:
                            current_table = policy['tablename']
                            f.write(f"\n-- Políticas para tabla: {current_table}\n")
                            f.write(f"ALTER TABLE {current_table} ENABLE ROW LEVEL SECURITY;\n\n")
                        
                        policy_stmt = f'CREATE POLICY "{policy["policyname"]}" ON {policy["tablename"]}'
                        policy_stmt += f' FOR {policy["cmd"]}'
                        
                        if policy['roles']:
                            policy_stmt += f' TO {", ".join(policy["roles"])}'
                        
                        if policy['qual']:
                            policy_stmt += f' USING ({policy["qual"]})'
                        
                        if policy['with_check']:
                            policy_stmt += f' WITH CHECK ({policy["with_check"]})'
                        
                        policy_stmt += ';\n'
                        f.write(policy_stmt)
                        
        self.logger.info(f"✓ Políticas RLS exportadas: {rls_file}")
        
    def generate_summary(self):
        """Generar resumen de exportación"""
        self.logger.info("Generando resumen de exportación...")
        
        summary_file = self.export_dir / "export_summary.md"
        
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                with open(summary_file, 'w', encoding='utf-8') as f:
                    f.write("# Resumen de Exportación - Sistema Municipal Chía\n\n")
                    f.write(f"**Fecha de exportación:** {datetime.now()}\n")
                    f.write(f"**Directorio:** {self.export_dir}\n\n")
                    f.write("## Tablas Exportadas\n\n")
                    
                    for table in self.main_tables:
                        try:
                            cur.execute(f"SELECT COUNT(*) FROM {table}")
                            count = cur.fetchone()[0]
                            f.write(f"- **{table}**: {count:,} registros\n")
                        except psycopg2.Error as e:
                            f.write(f"- **{table}**: Error obteniendo conteo ({e})\n")
                    
                    f.write("\n## Archivos Generados\n\n")
                    f.write("- `complete_structure.sql` - Estructura completa\n")
                    f.write("- `structure_[tabla].sql` - Estructura por tabla\n")
                    f.write("- `data_[tabla].sql` - Datos en formato SQL\n")
                    f.write("- `data_[tabla].csv` - Datos en formato CSV\n")
                    f.write("- `rls_policies.sql` - Políticas RLS\n")
                    f.write("- `restore_database.py` - Script de restauración\n\n")
                    
                    f.write("## Instrucciones de Restauración\n\n")
                    f.write("```bash\n")
                    f.write("python restore_database.py --host localhost --port 5432 --database chia_municipal --user postgres\n")
                    f.write("```\n")
                    
        self.logger.info(f"✓ Resumen generado: {summary_file}")
        
    def run_export(self):
        """Ejecutar exportación completa"""
        self.logger.info("🚀 Iniciando exportación completa...")
        
        try:
            # 1. Exportar estructura completa
            self.logger.info("1. Exportando estructura completa...")
            structure_file = self.export_dir / "complete_structure.sql"
            
            with open(structure_file, 'w', encoding='utf-8') as f:
                f.write("-- =============================================\n")
                f.write("-- ESTRUCTURA COMPLETA - SISTEMA MUNICIPAL CHÍA\n")
                f.write(f"-- Generado el: {datetime.now()}\n")
                f.write("-- =============================================\n\n")
                
                # Extensiones
                f.write("-- Extensiones requeridas\n")
                f.write('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";\n')
                f.write('CREATE EXTENSION IF NOT EXISTS "pgcrypto";\n')
                f.write('CREATE EXTENSION IF NOT EXISTS "vector";\n')
                f.write('CREATE EXTENSION IF NOT EXISTS "pg_trgm";\n\n')
                
                # Tipos personalizados
                f.write("-- Tipos personalizados\n")
                f.write("CREATE TYPE user_role AS ENUM ('ciudadano', 'admin', 'super_admin');\n")
                f.write("CREATE TYPE procedure_status AS ENUM ('draft', 'active', 'inactive', 'archived');\n")
                f.write("CREATE TYPE tramite_status AS ENUM ('pending', 'in_progress', 'completed', 'rejected', 'cancelled');\n")
                f.write("CREATE TYPE document_type AS ENUM ('cedula', 'pasaporte', 'cedula_extranjeria', 'nit');\n\n")
                
                # Estructura de tablas
                for table in self.main_tables:
                    structure = self.export_table_structure(table)
                    f.write(structure)
            
            # 2. Exportar datos de cada tabla
            self.logger.info("2. Exportando datos de tablas...")
            for table in self.main_tables:
                self.export_table_data_csv(table)
                self.export_table_data_sql(table)
            
            # 3. Exportar políticas RLS
            self.logger.info("3. Exportando políticas RLS...")
            self.export_rls_policies()
            
            # 4. Generar resumen
            self.logger.info("4. Generando resumen...")
            self.generate_summary()
            
            # 5. Calcular tamaño total
            total_size = sum(f.stat().st_size for f in self.export_dir.rglob('*') if f.is_file())
            total_size_mb = total_size / (1024 * 1024)
            
            self.logger.info("🎉 Exportación completada exitosamente!")
            self.logger.info(f"📁 Archivos generados en: {self.export_dir}")
            self.logger.info(f"💾 Tamaño total: {total_size_mb:.2f} MB")
            
        except Exception as e:
            self.logger.error(f"❌ Error durante la exportación: {e}")
            sys.exit(1)

if __name__ == "__main__":
    exporter = MunicipalDBExporter()
    exporter.run_export()
