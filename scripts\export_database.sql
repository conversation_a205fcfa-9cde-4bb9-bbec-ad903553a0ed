-- =============================================
-- SCRIPT DE EXPORTACIÓN COMPLETA - SISTEMA MUNICIPAL CHÍA
-- =============================================
-- Exporta estructura, datos y políticas RLS de las tablas principales
-- Fecha: 2025-01-06
-- Autor: Sistema Municipal Chía - DBA Export
-- =============================================

-- Configuración inicial
\echo 'Iniciando exportación de base de datos Sistema Municipal Chía...'
\echo 'Fecha: ' `date`

-- =============================================
-- 1. EXPORTAR ESTRUCTURA DE TABLAS PRINCIPALES
-- =============================================

\echo 'Exportando estructura de tablas...'

-- Crear archivo de estructura DDL
\o export_structure.sql

-- Header del archivo de estructura
SELECT '-- =============================================';
SELECT '-- ESTRUCTURA DE TABLAS - SISTEMA MUNICIPAL CHÍA';
SELECT '-- Generado automáticamente el: ' || NOW()::TEXT;
SELECT '-- =============================================';
SELECT '';

-- Extensiones requeridas
SELECT '-- Extensiones requeridas';
SELECT 'CREATE EXTENSION IF NOT EXISTS "uuid-ossp";';
SELECT 'CREATE EXTENSION IF NOT EXISTS "pgcrypto";';
SELECT 'CREATE EXTENSION IF NOT EXISTS "vector";';
SELECT 'CREATE EXTENSION IF NOT EXISTS "pg_trgm";';
SELECT '';

-- Tipos personalizados
SELECT '-- Tipos personalizados';
SELECT 'CREATE TYPE user_role AS ENUM (''ciudadano'', ''admin'', ''super_admin'');';
SELECT 'CREATE TYPE procedure_status AS ENUM (''draft'', ''active'', ''inactive'', ''archived'');';
SELECT 'CREATE TYPE tramite_status AS ENUM (''pending'', ''in_progress'', ''completed'', ''rejected'', ''cancelled'');';
SELECT 'CREATE TYPE document_type AS ENUM (''cedula'', ''pasaporte'', ''cedula_extranjeria'', ''nit'');';
SELECT '';

-- Exportar estructura usando información del catálogo del sistema
\echo 'Exportando estructura tabla dependencies...'
SELECT '-- Tabla: dependencies';
SELECT 'CREATE TABLE dependencies (';
SELECT string_agg(
    '  ' || column_name || ' ' ||
    CASE
        WHEN data_type = 'USER-DEFINED' THEN udt_name
        WHEN data_type = 'character varying' THEN 'TEXT'
        WHEN data_type = 'timestamp with time zone' THEN 'TIMESTAMP WITH TIME ZONE'
        ELSE UPPER(data_type)
    END ||
    CASE
        WHEN is_nullable = 'NO' THEN ' NOT NULL'
        ELSE ''
    END ||
    CASE
        WHEN column_default IS NOT NULL THEN ' DEFAULT ' || column_default
        ELSE ''
    END,
    ',' || chr(10)
) || chr(10) || ');'
FROM information_schema.columns
WHERE table_name = 'dependencies'
ORDER BY ordinal_position;
SELECT '';

\echo 'Exportando estructura tabla subdependencies...'
SELECT '-- Tabla: subdependencies';
SELECT 'CREATE TABLE subdependencies (';
SELECT string_agg(
    '  ' || column_name || ' ' ||
    CASE
        WHEN data_type = 'USER-DEFINED' THEN udt_name
        WHEN data_type = 'character varying' THEN 'TEXT'
        WHEN data_type = 'timestamp with time zone' THEN 'TIMESTAMP WITH TIME ZONE'
        ELSE UPPER(data_type)
    END ||
    CASE
        WHEN is_nullable = 'NO' THEN ' NOT NULL'
        ELSE ''
    END ||
    CASE
        WHEN column_default IS NOT NULL THEN ' DEFAULT ' || column_default
        ELSE ''
    END,
    ',' || chr(10)
) || chr(10) || ');'
FROM information_schema.columns
WHERE table_name = 'subdependencies'
ORDER BY ordinal_position;
SELECT '';

\o

-- =============================================
-- 2. EXPORTAR DATOS DE TABLAS PRINCIPALES EN CSV
-- =============================================

\echo 'Exportando datos de tablas en formato CSV...'

-- Exportar datos de dependencies
\echo 'Exportando datos tabla dependencies...'
\copy (SELECT * FROM dependencies ORDER BY created_at) TO 'export_dependencies_data.csv' WITH CSV HEADER;

-- Exportar datos de subdependencies
\echo 'Exportando datos tabla subdependencies...'
\copy (SELECT * FROM subdependencies ORDER BY dependency_id, created_at) TO 'export_subdependencies_data.csv' WITH CSV HEADER;

-- Exportar datos de procedures
\echo 'Exportando datos tabla procedures...'
\copy (SELECT * FROM procedures ORDER BY dependency_id, subdependency_id, created_at) TO 'export_procedures_data.csv' WITH CSV HEADER;

-- Exportar datos de opas
\echo 'Exportando datos tabla opas...'
\copy (SELECT * FROM opas ORDER BY dependency_id, subdependency_id, created_at) TO 'export_opas_data.csv' WITH CSV HEADER;

-- Exportar datos de faq_themes
\echo 'Exportando datos tabla faq_themes...'
\copy (SELECT * FROM faq_themes ORDER BY dependency_code, subdependency_code, display_order) TO 'export_faq_themes_data.csv' WITH CSV HEADER;

-- Exportar datos de municipal_faqs
\echo 'Exportando datos tabla municipal_faqs...'
\copy (SELECT * FROM municipal_faqs ORDER BY theme_id, display_order, created_at) TO 'export_municipal_faqs_data.csv' WITH CSV HEADER;

-- =============================================
-- 3. EXPORTAR DATOS EN FORMATO SQL INSERT
-- =============================================

\echo 'Generando archivos SQL con datos...'

-- Crear archivo de datos SQL
\o export_data_inserts.sql

SELECT '-- =============================================';
SELECT '-- DATOS DE TABLAS - SISTEMA MUNICIPAL CHÍA';
SELECT '-- Generado automáticamente el: ' || NOW()::TEXT;
SELECT '-- =============================================';
SELECT '';

-- Generar INSERTs para dependencies
SELECT '-- Datos tabla: dependencies';
SELECT 'TRUNCATE TABLE dependencies CASCADE;';

-- Generar INSERTs dinámicamente para dependencies
DO $$
DECLARE
    rec RECORD;
    insert_stmt TEXT;
BEGIN
    FOR rec IN SELECT * FROM dependencies ORDER BY created_at LOOP
        insert_stmt := 'INSERT INTO dependencies (id, code, name, acronym, description, contact_email, contact_phone, is_active, metadata, created_at, updated_at) VALUES (' ||
            quote_literal(rec.id::text) || ', ' ||
            quote_literal(rec.code) || ', ' ||
            quote_literal(rec.name) || ', ' ||
            COALESCE(quote_literal(rec.acronym), 'NULL') || ', ' ||
            COALESCE(quote_literal(rec.description), 'NULL') || ', ' ||
            COALESCE(quote_literal(rec.contact_email), 'NULL') || ', ' ||
            COALESCE(quote_literal(rec.contact_phone), 'NULL') || ', ' ||
            rec.is_active || ', ' ||
            quote_literal(rec.metadata::text) || ', ' ||
            quote_literal(rec.created_at::text) || ', ' ||
            quote_literal(rec.updated_at::text) || ');';
        RAISE NOTICE '%', insert_stmt;
    END LOOP;
END $$;

SELECT '';

\o

-- =============================================
-- 4. EXPORTAR POLÍTICAS RLS
-- =============================================

\echo 'Exportando políticas RLS...'

\o export_rls_policies.sql

SELECT '-- =============================================';
SELECT '-- POLÍTICAS RLS - SISTEMA MUNICIPAL CHÍA';
SELECT '-- Generado automáticamente el: ' || NOW()::TEXT;
SELECT '-- =============================================';
SELECT '';

-- Exportar políticas RLS para cada tabla
SELECT '-- Políticas RLS para dependencies';
SELECT 'ALTER TABLE dependencies ENABLE ROW LEVEL SECURITY;';
SELECT string_agg(
    'CREATE POLICY "' || policyname || '" ON ' || tablename || 
    ' FOR ' || cmd || 
    CASE WHEN roles IS NOT NULL THEN ' TO ' || array_to_string(roles, ', ') ELSE '' END ||
    CASE WHEN qual IS NOT NULL THEN ' USING (' || qual || ')' ELSE '' END ||
    CASE WHEN with_check IS NOT NULL THEN ' WITH CHECK (' || with_check || ')' ELSE '' END || ';',
    E'\n'
)
FROM pg_policies 
WHERE tablename = 'dependencies';
SELECT '';

\o

\echo 'Exportación completada exitosamente!'
\echo 'Archivos generados:'
\echo '- export_structure.sql (Estructura de tablas)'
\echo '- export_data.sql (Datos en formato SQL)'
\echo '- export_rls_policies.sql (Políticas RLS)'
\echo '- export_*_data.csv (Datos en formato CSV)'
