# Sistema de Consolidación JSON - Municipal Chía

## 📋 Descripción General

Este sistema genera un archivo JSON consolidado que contiene toda la información estructurada jerárquicamente del sistema municipal de Chía, incluyendo dependencias, subdependencias, trámites, OPAs y FAQs.

## 🎯 Objetivo

Crear una **fuente única de verdad** para el sistema municipal que facilite:
- APIs REST del sistema municipal
- Interfaces de usuario dinámicas
- Análisis de datos y reportes
- Sincronización con otros sistemas
- Respaldo estructurado de información

## 📁 Estructura del Proyecto

```
scripts/
├── generate_consolidated_json.py      # Generador principal
├── validate_consolidated_json.py      # Validador y optimizador
├── setup_env_consolidation.py        # Configurador de entorno
├── run_consolidation.py              # Script de ejecución simplificado
├── README_CONSOLIDACION_JSON.md       # Esta documentación
└── outputs/                          # Archivos generados
    ├── sistema_municipal_chia_consolidado.json
    ├── sistema_municipal_chia_consolidado_optimized.json
    ├── consolidation_summary.md
    └── validation_report_YYYYMMDD_HHMMSS.md
```

## 🚀 Instalación y Configuración

### 1. Requisitos Previos

```bash
# Python 3.8 o superior
python --version

# Instalar dependencias
pip install psycopg2-binary python-dotenv
```

### 2. Configuración Automática

```bash
# Ejecutar configurador automático
python scripts/setup_env_consolidation.py
```

El configurador:
- ✅ Verifica dependencias de Python
- 🔐 Solicita credenciales de Supabase
- 🔌 Prueba la conexión a la base de datos
- 💾 Guarda configuración en `.env`
- 📝 Crea scripts de ejecución

### 3. Configuración Manual

Si prefieres configurar manualmente, crea un archivo `.env`:

```bash
# Configuración de base de datos Supabase
SUPABASE_DB_HOST="db.zeieudvbhlrlnfkwejoh.supabase.co"
SUPABASE_DB_PORT="5432"
SUPABASE_DB_NAME="postgres"
SUPABASE_DB_USER="postgres"
SUPABASE_DB_PASSWORD="tu_password_aqui"
```

## 🏃‍♂️ Ejecución

### Opción 1: Ejecución Automática (Recomendada)

```bash
# Ejecutar proceso completo
python scripts/run_consolidation.py
```

### Opción 2: Ejecución Manual

```bash
# 1. Generar JSON consolidado
python scripts/generate_consolidated_json.py

# 2. Validar JSON generado
python scripts/validate_consolidated_json.py scripts/sistema_municipal_chia_consolidado.json
```

## 📊 Estructura del JSON Generado

### Esquema Jerárquico

```json
{
  "metadata": {
    "generated_at": "2025-01-06T10:30:00",
    "generator": "Sistema Municipal Chía - JSON Consolidator",
    "version": "1.0.0",
    "total_dependencies": 12,
    "total_subdependencies": 75,
    "total_tramites": 108,
    "total_opas": 721,
    "total_faqs": 150,
    "total_procedures": 829
  },
  "dependencies": [
    {
      "id": "uuid",
      "code": "SEC-GOB",
      "name": "Secretaría de Gobierno",
      "acronym": "SEGOB",
      "description": "...",
      "contact_email": "<EMAIL>",
      "contact_phone": "+57 1 123-4567",
      "counters": {
        "total_tramites": 25,
        "total_opas": 45,
        "total_procedures": 70
      },
      "subdependencies": [
        {
          "id": "uuid",
          "code": "INSP-POL",
          "name": "Inspección de Policía",
          "counters": {
            "tramites_count": 8,
            "opas_count": 12,
            "faq_themes_count": 3,
            "total_faqs_count": 15
          },
          "tramites": [
            {
              "id": "uuid",
              "name": "Certificado de Residencia",
              "description": "...",
              "response_time": "3 días hábiles",
              "has_cost": true,
              "cost_description": "$15,000 COP",
              "requirements": ["Cédula", "Recibo servicios"],
              "suit_url": "https://suit.gov.co/...",
              "gov_url": "https://gov.co/..."
            }
          ],
          "opas": [
            {
              "id": "uuid",
              "code": "OPA-001",
              "name": "Procedimiento Administrativo X",
              "description": "...",
              "has_cost": false,
              "status": "active"
            }
          ],
          "faq_themes": [
            {
              "theme": {
                "id": "uuid",
                "name": "Certificaciones",
                "description": "...",
                "icon": "certificate",
                "color": "#059669"
              },
              "faqs": [
                {
                  "id": "uuid",
                  "question": "¿Cómo solicitar certificado de residencia?",
                  "answer": "Para solicitar...",
                  "keywords": ["certificado", "residencia"],
                  "related_procedures": ["tramite-001"],
                  "tema": {
                    "id": "uuid",
                    "name": "Certificaciones",
                    "description": "...",
                    "icon": "certificate",
                    "color": "#059669"
                  }
                }
              ],
              "faqs_count": 5
            }
          ]
        }
      ]
    }
  ]
}
```

### Campos por Entidad

#### Dependencias
- `id`, `code`, `name`, `acronym`, `description`
- `contact_email`, `contact_phone`, `address`
- `is_active`, `created_at`, `updated_at`
- `counters`: totales agregados

#### Subdependencias
- `id`, `dependency_id`, `code`, `name`, `acronym`, `description`
- `contact_email`, `contact_phone`
- `is_active`, `created_at`, `updated_at`
- `counters`: contadores específicos

#### Trámites
- `id`, `name`, `description`, `response_time`
- `has_cost`, `cost_description`
- `requirements`, `documents_required`
- `suit_url`, `gov_url`, `form_required`
- `status`, `metadata`

#### OPAs
- `id`, `code`, `name`, `description`
- `has_cost`, `cost_description`
- `status`, `metadata`

#### FAQs
- `id`, `question`, `answer`, `keywords`
- `related_procedures`, `display_order`
- `popularity_score`, `view_count`
- `helpful_votes`, `unhelpful_votes`
- `tema`: información del tema asociado

## 🔍 Validación y Optimización

### Validaciones Realizadas

1. **Estructura Básica**
   - Presencia de secciones requeridas
   - Tipos de datos correctos
   - Campos obligatorios

2. **Integridad Referencial**
   - Unicidad de IDs y códigos
   - Relaciones válidas entre entidades
   - Consistencia de contadores

3. **Calidad de Datos**
   - Campos vacíos o nulos
   - Formato de datos
   - Completitud de información

### Optimizaciones Aplicadas

- Eliminación de campos nulos
- Compresión de arrays vacíos
- Optimización de formato JSON
- Reducción de tamaño de archivo

## 📈 Monitoreo y Reportes

### Archivos Generados

1. **`sistema_municipal_chia_consolidado.json`**
   - Archivo principal con toda la información

2. **`sistema_municipal_chia_consolidado_optimized.json`**
   - Versión optimizada (menor tamaño)

3. **`consolidation_summary.md`**
   - Resumen estadístico de la consolidación

4. **`validation_report_YYYYMMDD_HHMMSS.md`**
   - Reporte detallado de validación

### Métricas Incluidas

- Total de registros por tipo
- Tamaño de archivos generados
- Tiempo de procesamiento
- Errores y advertencias encontrados
- Sugerencias de optimización

## 🔧 Solución de Problemas

### Errores Comunes

#### Error de Conexión a Base de Datos
```bash
❌ Error conectando a la base de datos: connection failed
```
**Solución:**
1. Verificar credenciales en `.env`
2. Confirmar conectividad de red
3. Validar permisos de usuario

#### Dependencias Faltantes
```bash
❌ Error: psycopg2 no está instalado
```
**Solución:**
```bash
pip install psycopg2-binary python-dotenv
```

#### Archivo JSON Inválido
```bash
❌ Error de formato JSON: Expecting ',' delimiter
```
**Solución:**
1. Ejecutar validador: `python scripts/validate_consolidated_json.py`
2. Revisar reporte de errores
3. Regenerar archivo si es necesario

### Logs y Debugging

Los scripts generan logs detallados que incluyen:
- Timestamps de cada operación
- Contadores de registros procesados
- Errores específicos con contexto
- Sugerencias de corrección

## 🔄 Mantenimiento

### Actualización de Datos

Para mantener el JSON actualizado:

```bash
# Ejecutar consolidación periódicamente
python scripts/run_consolidation.py
```

### Backup y Versionado

- Los archivos anteriores se respaldan automáticamente
- Cada generación incluye timestamp
- Mantener historial de versiones para auditoría

## 📞 Soporte

Para problemas o mejoras:

1. Revisar logs de ejecución
2. Consultar reportes de validación
3. Verificar configuración de entorno
4. Contactar al equipo de desarrollo

## 🔐 Consideraciones de Seguridad

- Las credenciales se almacenan en `.env` (no versionar)
- Acceso de solo lectura a la base de datos
- Validación de datos antes de procesamiento
- Logs no contienen información sensible

---

**Versión:** 1.0.0  
**Última actualización:** 2025-01-06  
**Autor:** Sistema Municipal Chía - Development Team
