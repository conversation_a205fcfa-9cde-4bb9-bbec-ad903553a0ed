'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs'
import { 
  Plus,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  Upload,
  Download,
  Eye,
  Edit,
  Bell,
  Calendar,
  User,
  Building,
  Phone,
  Mail,
  MapPin,
  Search,
  Filter,
  RefreshCw,
  Archive,
  Star,
  MessageSquare
} from 'lucide-react'
import { ProcedureInitiationModal } from './ProcedureInitiationModal'
import { ProcedureDetailView } from './ProcedureDetailView'
import { DocumentUploadModal } from './DocumentUploadModal'
import { NotificationCenter } from './NotificationCenter'

interface UserProcedure {
  id: string
  reference_number: string
  created_at: string
  updated_at: string
  estimated_completion: string | null
  actual_completion: string | null
  priority: number | null
  notes: string | null
  attachments: string[] | null
  submitted_data: any
  procedure: {
    id: string
    name: string
    description: string
    cost: number | null
    response_time: string | null
    category: string | null
    requirements: string[]
    documents_required: string[]
    dependency: {
      id: string
      name: string
      acronym: string
      contact_email: string
      contact_phone: string
      address: string
    }
  }
  status: {
    id: string
    name: string
    display_name: string
    description: string
    color: string
    is_final: boolean
    order_index: number
  }
  assigned_to: {
    id: string
    full_name: string
    email: string
  } | null
}

interface ProcedureManagementInterfaceProps {
  userProcedures: UserProcedure[]
  availableProcedures: any[]
  procedureStatuses: any[]
  notifications: any[]
  currentUser: any
  userProfile: any
}

export function ProcedureManagementInterface({
  userProcedures,
  availableProcedures,
  procedureStatuses,
  notifications,
  currentUser,
  userProfile
}: ProcedureManagementInterfaceProps) {
  const [activeTab, setActiveTab] = useState('active')
  const [selectedProcedure, setSelectedProcedure] = useState<UserProcedure | null>(null)
  const [showInitiationModal, setShowInitiationModal] = useState(false)
  const [showDocumentModal, setShowDocumentModal] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [refreshing, setRefreshing] = useState(false)

  // Filter procedures based on tab and filters
  const filteredProcedures = userProcedures.filter(procedure => {
    const matchesSearch = !searchTerm || 
      procedure.procedure.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      procedure.reference_number.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = !statusFilter || procedure.status.name === statusFilter
    
    const matchesTab = activeTab === 'all' || 
      (activeTab === 'active' && !procedure.status.is_final) ||
      (activeTab === 'completed' && procedure.status.is_final) ||
      (activeTab === 'draft' && procedure.status.name === 'draft')
    
    return matchesSearch && matchesStatus && matchesTab
  })

  // Get procedure statistics
  const stats = {
    total: userProcedures.length,
    active: userProcedures.filter(p => !p.status.is_final).length,
    completed: userProcedures.filter(p => p.status.is_final && p.status.name === 'completed').length,
    pending: userProcedures.filter(p => p.status.name === 'submitted' || p.status.name === 'in_review').length,
    draft: userProcedures.filter(p => p.status.name === 'draft').length
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    // Refresh would be handled by parent component or router refresh
    setTimeout(() => setRefreshing(false), 1000)
  }

  const getStatusColor = (status: any) => {
    return status.color || '#6B7280'
  }

  const getPriorityBadge = (priority: number | null) => {
    if (!priority) return null
    
    const priorityConfig = {
      1: { label: 'Baja', color: 'bg-green-100 text-green-800' },
      2: { label: 'Media', color: 'bg-yellow-100 text-yellow-800' },
      3: { label: 'Alta', color: 'bg-red-100 text-red-800' }
    }
    
    const config = priorityConfig[priority as keyof typeof priorityConfig]
    return config ? (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    ) : null
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-CO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getDaysRemaining = (estimatedCompletion: string | null) => {
    if (!estimatedCompletion) return null
    
    const today = new Date()
    const completion = new Date(estimatedCompletion)
    const diffTime = completion.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return diffDays
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <FileText className="h-8 w-8 text-blue-600" />
              <div>
                <p className="text-sm text-gray-500">Total</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <Clock className="h-8 w-8 text-orange-600" />
              <div>
                <p className="text-sm text-gray-500">Activos</p>
                <p className="text-2xl font-bold">{stats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div>
                <p className="text-sm text-gray-500">Completados</p>
                <p className="text-2xl font-bold">{stats.completed}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <AlertCircle className="h-8 w-8 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-500">Pendientes</p>
                <p className="text-2xl font-bold">{stats.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <Edit className="h-8 w-8 text-gray-600" />
              <div>
                <p className="text-sm text-gray-500">Borradores</p>
                <p className="text-2xl font-bold">{stats.draft}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Action Bar */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
            <div>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5 text-blue-600" />
                Mis Trámites
              </CardTitle>
              <CardDescription>
                Gestiona y da seguimiento a todos tus trámites municipales
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowNotifications(true)}
                className="relative"
              >
                <Bell className="h-4 w-4 mr-2" />
                Notificaciones
                {notifications.length > 0 && (
                  <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs">
                    {notifications.length}
                  </Badge>
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={refreshing}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Actualizar
              </Button>
              <Button
                size="sm"
                onClick={() => setShowInitiationModal(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Nuevo Trámite
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Buscar por nombre o número de referencia..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                aria-label="Filtrar por estado"
              >
                <option value="">Todos los estados</option>
                {procedureStatuses.map((status) => (
                  <option key={status.id} value={status.name}>
                    {status.display_name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Procedures Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="active">
            Activos ({stats.active})
          </TabsTrigger>
          <TabsTrigger value="completed">
            Completados ({stats.completed})
          </TabsTrigger>
          <TabsTrigger value="draft">
            Borradores ({stats.draft})
          </TabsTrigger>
          <TabsTrigger value="all">
            Todos ({stats.total})
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          {filteredProcedures.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No hay trámites
                </h3>
                <p className="text-gray-500 mb-4">
                  {activeTab === 'active' && 'No tienes trámites activos en este momento.'}
                  {activeTab === 'completed' && 'No tienes trámites completados aún.'}
                  {activeTab === 'draft' && 'No tienes borradores guardados.'}
                  {activeTab === 'all' && 'No has iniciado ningún trámite aún.'}
                </p>
                <Button onClick={() => setShowInitiationModal(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Iniciar Primer Trámite
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {filteredProcedures.map((procedure) => (
                <Card key={procedure.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                      {/* Main Info */}
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900">
                              {procedure.procedure.name}
                            </h3>
                            <p className="text-sm text-gray-500">
                              Ref: {procedure.reference_number}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge
                              style={{ backgroundColor: getStatusColor(procedure.status) }}
                              className="text-white"
                            >
                              {procedure.status.display_name}
                            </Badge>
                            {getPriorityBadge(procedure.priority)}
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                          <div className="flex items-center">
                            <Building className="h-4 w-4 mr-2 text-gray-400" />
                            {procedure.procedure.dependency.name}
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                            Creado: {formatDate(procedure.created_at)}
                          </div>
                          {procedure.estimated_completion && (
                            <div className="flex items-center">
                              <Clock className="h-4 w-4 mr-2 text-gray-400" />
                              {(() => {
                                const days = getDaysRemaining(procedure.estimated_completion)
                                return days !== null ? (
                                  days > 0 ? `${days} días restantes` :
                                  days === 0 ? 'Vence hoy' :
                                  `Vencido hace ${Math.abs(days)} días`
                                ) : 'Sin fecha límite'
                              })()}
                            </div>
                          )}
                        </div>

                        {procedure.assigned_to && (
                          <div className="mt-2 flex items-center text-sm text-gray-600">
                            <User className="h-4 w-4 mr-2 text-gray-400" />
                            Asignado a: {procedure.assigned_to.full_name}
                          </div>
                        )}

                        {procedure.attachments && procedure.attachments.length > 0 && (
                          <div className="mt-2 flex items-center text-sm text-gray-600">
                            <Upload className="h-4 w-4 mr-2 text-gray-400" />
                            {procedure.attachments.length} documento(s) adjunto(s)
                          </div>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedProcedure(procedure)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          Ver Detalles
                        </Button>

                        {!procedure.status.is_final && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedProcedure(procedure)
                              setShowDocumentModal(true)
                            }}
                          >
                            <Upload className="h-4 w-4 mr-2" />
                            Documentos
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Modals */}
      {showInitiationModal && (
        <ProcedureInitiationModal
          availableProcedures={availableProcedures}
          onClose={() => setShowInitiationModal(false)}
          currentUser={currentUser}
        />
      )}

      {selectedProcedure && !showDocumentModal && (
        <ProcedureDetailView
          procedure={selectedProcedure}
          onClose={() => setSelectedProcedure(null)}
          onUploadDocuments={() => {
            setShowDocumentModal(true)
          }}
        />
      )}

      {showDocumentModal && selectedProcedure && (
        <DocumentUploadModal
          procedure={selectedProcedure}
          onClose={() => {
            setShowDocumentModal(false)
            setSelectedProcedure(null)
          }}
        />
      )}

      {showNotifications && (
        <NotificationCenter
          notifications={notifications}
          onClose={() => setShowNotifications(false)}
        />
      )}
    </div>
  )
}
