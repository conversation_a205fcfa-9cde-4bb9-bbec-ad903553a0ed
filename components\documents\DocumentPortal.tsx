'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { 
  FileText,
  Folder,
  Upload,
  Download,
  Search,
  Filter,
  Grid,
  List,
  Calendar,
  HardDrive,
  Eye,
  Trash2,
  Share2,
  Star,
  Clock,
  Building,
  User,
  BarChart3
} from 'lucide-react'
import { DocumentGrid } from './DocumentGrid'
import { DocumentList } from './DocumentList'
import { DocumentUploadZone } from './DocumentUploadZone'
import { DocumentSearch } from './DocumentSearch'
import { DocumentStats } from './DocumentStats'
import { RecentActivity } from './RecentActivity'

interface DocumentPortalProps {
  user: any
  profile: any
  procedures: any[]
  storageFiles: any[]
  statistics: {
    totalDocuments: number
    proceduresWithDocuments: number
    totalStorageUsed: number
    storageLimit: number
  }
  recentActivity: any[]
}

export function DocumentPortal({
  user,
  profile,
  procedures,
  storageFiles,
  statistics,
  recentActivity
}: DocumentPortalProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [showUploadZone, setShowUploadZone] = useState(false)

  // Process documents from procedures
  const allDocuments = procedures.flatMap(procedure => 
    (procedure.attachments || []).map((filePath: string) => ({
      id: filePath,
      name: filePath.split('/').pop() || 'Documento',
      path: filePath,
      procedureId: procedure.id,
      procedureName: procedure.procedure.name,
      referenceNumber: procedure.reference_number,
      dependency: procedure.procedure.dependency.name,
      status: procedure.status,
      uploadedAt: procedure.updated_at,
      size: 0, // We don't have size info from attachments
      type: getFileType(filePath)
    }))
  )

  // Filter documents based on search and category
  const filteredDocuments = allDocuments.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         doc.procedureName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         doc.referenceNumber.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesCategory = selectedCategory === 'all' || 
                           selectedCategory === doc.type ||
                           (selectedCategory === 'recent' && isRecentDocument(doc.uploadedAt))
    
    return matchesSearch && matchesCategory
  })

  // Group documents by procedure
  const documentsByProcedure = procedures.reduce((acc, procedure) => {
    if (procedure.attachments && procedure.attachments.length > 0) {
      acc[procedure.id] = {
        procedure,
        documents: procedure.attachments.map((filePath: string) => ({
          id: filePath,
          name: filePath.split('/').pop() || 'Documento',
          path: filePath,
          uploadedAt: procedure.updated_at,
          type: getFileType(filePath)
        }))
      }
    }
    return acc
  }, {} as Record<string, any>)

  function getFileType(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase()
    switch (extension) {
      case 'pdf': return 'pdf'
      case 'doc':
      case 'docx': return 'document'
      case 'jpg':
      case 'jpeg':
      case 'png': return 'image'
      default: return 'file'
    }
  }

  function isRecentDocument(dateString: string): boolean {
    const date = new Date(dateString)
    const now = new Date()
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))
    return diffInDays <= 7
  }

  const formatStorageSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const storageUsagePercentage = (statistics.totalStorageUsed / statistics.storageLimit) * 100

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="bg-gradient-to-r from-primary to-primary/90 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">Portal de Documentos</h1>
              <p className="text-white/80">
                Gestiona todos tus documentos y archivos de trámites municipales
              </p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold">{statistics.totalDocuments}</div>
              <div className="text-white/80 text-sm">Documentos totales</div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Documentos</p>
                <p className="text-2xl font-bold text-gray-900">{statistics.totalDocuments}</p>
              </div>
              <FileText className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Trámites con Docs</p>
                <p className="text-2xl font-bold text-gray-900">{statistics.proceduresWithDocuments}</p>
              </div>
              <Folder className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Almacenamiento</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatStorageSize(statistics.totalStorageUsed)}
                </p>
                <Progress value={storageUsagePercentage} className="mt-2" />
              </div>
              <HardDrive className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Documentos Recientes</p>
                <p className="text-2xl font-bold text-gray-900">
                  {allDocuments.filter(doc => isRecentDocument(doc.uploadedAt)).length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Left Sidebar */}
        <div className="lg:col-span-1 space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Acciones Rápidas</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                onClick={() => setShowUploadZone(true)}
                className="w-full justify-start"
              >
                <Upload className="h-4 w-4 mr-2" />
                Subir Documentos
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Download className="h-4 w-4 mr-2" />
                Descargar Todo
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Share2 className="h-4 w-4 mr-2" />
                Compartir
              </Button>
            </CardContent>
          </Card>

          {/* Categories */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Categorías</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {[
                { id: 'all', name: 'Todos los documentos', count: allDocuments.length },
                { id: 'recent', name: 'Recientes (7 días)', count: allDocuments.filter(doc => isRecentDocument(doc.uploadedAt)).length },
                { id: 'pdf', name: 'PDFs', count: allDocuments.filter(doc => doc.type === 'pdf').length },
                { id: 'image', name: 'Imágenes', count: allDocuments.filter(doc => doc.type === 'image').length },
                { id: 'document', name: 'Documentos', count: allDocuments.filter(doc => doc.type === 'document').length }
              ].map(category => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                    selectedCategory === category.id
                      ? 'bg-primary/10 text-primary'
                      : 'hover:bg-gray-100 text-gray-700'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span>{category.name}</span>
                    <Badge variant="secondary" className="text-xs">
                      {category.count}
                    </Badge>
                  </div>
                </button>
              ))}
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <RecentActivity activities={recentActivity} />
        </div>

        {/* Main Content Area */}
        <div className="lg:col-span-3 space-y-6">
          {/* Search and View Controls */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between space-x-4">
                <div className="flex-1">
                  <DocumentSearch 
                    searchQuery={searchQuery}
                    onSearchChange={setSearchQuery}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Documents Display */}
          <Tabs defaultValue="all" className="space-y-6">
            <TabsList>
              <TabsTrigger value="all">Todos los Documentos</TabsTrigger>
              <TabsTrigger value="by-procedure">Por Trámite</TabsTrigger>
              <TabsTrigger value="statistics">Estadísticas</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="space-y-6">
              {viewMode === 'grid' ? (
                <DocumentGrid documents={filteredDocuments} />
              ) : (
                <DocumentList documents={filteredDocuments} />
              )}
            </TabsContent>

            <TabsContent value="by-procedure" className="space-y-6">
              <div className="space-y-6">
                {Object.entries(documentsByProcedure).map(([procedureId, data]) => (
                  <Card key={procedureId}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-lg">{data.procedure.procedure.name}</CardTitle>
                          <CardDescription>
                            {data.procedure.reference_number} • {data.procedure.procedure.dependency.name}
                          </CardDescription>
                        </div>
                        <Badge 
                          style={{ backgroundColor: data.procedure.status.color }}
                          className="text-white"
                        >
                          {data.procedure.status.display_name}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <DocumentGrid documents={data.documents} compact />
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="statistics">
              <DocumentStats 
                documents={allDocuments}
                procedures={procedures}
                statistics={statistics}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Upload Zone Modal */}
      {showUploadZone && (
        <DocumentUploadZone
          user={user}
          procedures={procedures}
          onClose={() => setShowUploadZone(false)}
        />
      )}
    </div>
  )
}
