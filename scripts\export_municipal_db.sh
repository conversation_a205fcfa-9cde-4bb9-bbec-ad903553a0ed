#!/bin/bash

# =============================================
# SCRIPT DE EXPORTACIÓN COMPLETA - SISTEMA MUNICIPAL CHÍA
# =============================================
# Exporta estructura, datos y políticas RLS de las tablas principales
# Fecha: 2025-01-06
# Autor: Sistema Municipal Chía - DBA Export
# =============================================

# Configuración de colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuración de la base de datos
DB_HOST="${SUPABASE_DB_HOST:-db.zeieudvbhlrlnfkwejoh.supabase.co}"
DB_PORT="${SUPABASE_DB_PORT:-5432}"
DB_NAME="${SUPABASE_DB_NAME:-postgres}"
DB_USER="${SUPABASE_DB_USER:-postgres}"
DB_PASSWORD="${SUPABASE_DB_PASSWORD}"

# Directorio de exportación
EXPORT_DIR="./database_exports/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$EXPORT_DIR"

# Función para logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Verificar que pg_dump esté disponible
if ! command -v pg_dump &> /dev/null; then
    error "pg_dump no está instalado. Por favor instala PostgreSQL client tools."
    exit 1
fi

# Verificar variables de entorno
if [ -z "$DB_PASSWORD" ]; then
    error "La variable SUPABASE_DB_PASSWORD no está configurada."
    echo "Por favor configura las variables de entorno de Supabase:"
    echo "export SUPABASE_DB_PASSWORD='tu_password'"
    exit 1
fi

log "Iniciando exportación de base de datos Sistema Municipal Chía..."
log "Directorio de exportación: $EXPORT_DIR"

# =============================================
# 1. EXPORTAR ESTRUCTURA COMPLETA
# =============================================

log "Exportando estructura completa de la base de datos..."

pg_dump \
    --host="$DB_HOST" \
    --port="$DB_PORT" \
    --username="$DB_USER" \
    --dbname="$DB_NAME" \
    --schema-only \
    --no-owner \
    --no-privileges \
    --verbose \
    --file="$EXPORT_DIR/01_complete_structure.sql"

if [ $? -eq 0 ]; then
    log "✓ Estructura completa exportada exitosamente"
else
    error "✗ Error al exportar estructura completa"
    exit 1
fi

# =============================================
# 2. EXPORTAR TABLAS PRINCIPALES CON DATOS
# =============================================

# Lista de tablas principales a exportar
MAIN_TABLES=(
    "dependencies"
    "subdependencies" 
    "procedures"
    "opas"
    "faq_themes"
    "municipal_faqs"
)

log "Exportando tablas principales con datos..."

for table in "${MAIN_TABLES[@]}"; do
    info "Exportando tabla: $table"
    
    # Exportar estructura de la tabla
    pg_dump \
        --host="$DB_HOST" \
        --port="$DB_PORT" \
        --username="$DB_USER" \
        --dbname="$DB_NAME" \
        --table="$table" \
        --schema-only \
        --no-owner \
        --no-privileges \
        --file="$EXPORT_DIR/structure_${table}.sql"
    
    # Exportar datos de la tabla en formato SQL
    pg_dump \
        --host="$DB_HOST" \
        --port="$DB_PORT" \
        --username="$DB_USER" \
        --dbname="$DB_NAME" \
        --table="$table" \
        --data-only \
        --no-owner \
        --no-privileges \
        --column-inserts \
        --file="$EXPORT_DIR/data_${table}.sql"
    
    # Exportar datos en formato CSV
    psql \
        --host="$DB_HOST" \
        --port="$DB_PORT" \
        --username="$DB_USER" \
        --dbname="$DB_NAME" \
        --command="\copy (SELECT * FROM $table ORDER BY created_at) TO '$EXPORT_DIR/data_${table}.csv' WITH CSV HEADER;"
    
    if [ $? -eq 0 ]; then
        log "✓ Tabla $table exportada exitosamente"
    else
        warning "✗ Error al exportar tabla $table"
    fi
done

# =============================================
# 3. EXPORTAR POLÍTICAS RLS
# =============================================

log "Exportando políticas RLS..."

psql \
    --host="$DB_HOST" \
    --port="$DB_PORT" \
    --username="$DB_USER" \
    --dbname="$DB_NAME" \
    --output="$EXPORT_DIR/03_rls_policies.sql" \
    --command="
-- =============================================
-- POLÍTICAS RLS - SISTEMA MUNICIPAL CHÍA
-- Generado automáticamente el: $(date)
-- =============================================

-- Funciones auxiliares para RLS
SELECT 'CREATE OR REPLACE FUNCTION get_user_role() RETURNS TEXT AS \$\$' || chr(10) ||
       'BEGIN' || chr(10) ||
       '  RETURN (' || chr(10) ||
       '    SELECT r.name' || chr(10) ||
       '    FROM profiles p' || chr(10) ||
       '    JOIN roles r ON p.role_id = r.id' || chr(10) ||
       '    WHERE p.id = auth.uid()' || chr(10) ||
       '  );' || chr(10) ||
       'END;' || chr(10) ||
       '\$\$ LANGUAGE plpgsql SECURITY DEFINER;' || chr(10);

-- Exportar todas las políticas RLS
SELECT 
    'ALTER TABLE ' || tablename || ' ENABLE ROW LEVEL SECURITY;' || chr(10) ||
    'CREATE POLICY \"' || policyname || '\" ON ' || tablename || 
    ' FOR ' || cmd || 
    CASE WHEN roles IS NOT NULL THEN ' TO ' || array_to_string(roles, ', ') ELSE '' END ||
    CASE WHEN qual IS NOT NULL THEN ' USING (' || qual || ')' ELSE '' END ||
    CASE WHEN with_check IS NOT NULL THEN ' WITH CHECK (' || with_check || ')' ELSE '' END || ';' || chr(10)
FROM pg_policies 
WHERE tablename IN ('dependencies', 'subdependencies', 'procedures', 'opas', 'faq_themes', 'municipal_faqs')
ORDER BY tablename, policyname;
"

if [ $? -eq 0 ]; then
    log "✓ Políticas RLS exportadas exitosamente"
else
    warning "✗ Error al exportar políticas RLS"
fi

# =============================================
# 4. EXPORTAR FUNCIONES Y TRIGGERS
# =============================================

log "Exportando funciones y triggers..."

pg_dump \
    --host="$DB_HOST" \
    --port="$DB_PORT" \
    --username="$DB_USER" \
    --dbname="$DB_NAME" \
    --schema-only \
    --no-owner \
    --no-privileges \
    --no-tablespaces \
    --no-security-labels \
    --section=pre-data \
    --section=post-data \
    --file="$EXPORT_DIR/04_functions_triggers.sql"

if [ $? -eq 0 ]; then
    log "✓ Funciones y triggers exportados exitosamente"
else
    warning "✗ Error al exportar funciones y triggers"
fi

# =============================================
# 5. GENERAR ESTADÍSTICAS DE EXPORTACIÓN
# =============================================

log "Generando estadísticas de exportación..."

cat > "$EXPORT_DIR/00_export_summary.md" << EOF
# Resumen de Exportación - Sistema Municipal Chía

**Fecha de exportación:** $(date)
**Directorio:** $EXPORT_DIR

## Tablas Exportadas

EOF

for table in "${MAIN_TABLES[@]}"; do
    count=$(psql --host="$DB_HOST" --port="$DB_PORT" --username="$DB_USER" --dbname="$DB_NAME" --tuples-only --command="SELECT COUNT(*) FROM $table;")
    echo "- **$table**: $count registros" >> "$EXPORT_DIR/00_export_summary.md"
done

cat >> "$EXPORT_DIR/00_export_summary.md" << EOF

## Archivos Generados

- \`01_complete_structure.sql\` - Estructura completa de la base de datos
- \`structure_[tabla].sql\` - Estructura individual por tabla
- \`data_[tabla].sql\` - Datos en formato SQL INSERT
- \`data_[tabla].csv\` - Datos en formato CSV
- \`03_rls_policies.sql\` - Políticas de Row Level Security
- \`04_functions_triggers.sql\` - Funciones y triggers

## Instrucciones de Restauración

1. Crear nueva base de datos
2. Ejecutar \`01_complete_structure.sql\`
3. Ejecutar archivos \`data_[tabla].sql\` en orden de dependencias
4. Ejecutar \`03_rls_policies.sql\`
5. Ejecutar \`04_functions_triggers.sql\`

EOF

log "✓ Estadísticas generadas exitosamente"

# =============================================
# 6. CREAR ARCHIVO DE RESTAURACIÓN
# =============================================

log "Creando script de restauración..."

cat > "$EXPORT_DIR/restore_database.sh" << 'EOF'
#!/bin/bash

# Script de restauración para Sistema Municipal Chía
# Uso: ./restore_database.sh [DB_HOST] [DB_PORT] [DB_NAME] [DB_USER]

DB_HOST="${1:-localhost}"
DB_PORT="${2:-5432}"
DB_NAME="${3:-chia_municipal}"
DB_USER="${4:-postgres}"

echo "Restaurando base de datos Sistema Municipal Chía..."
echo "Host: $DB_HOST:$DB_PORT"
echo "Database: $DB_NAME"
echo "User: $DB_USER"

# 1. Crear estructura
echo "1. Creando estructura..."
psql --host="$DB_HOST" --port="$DB_PORT" --username="$DB_USER" --dbname="$DB_NAME" --file="01_complete_structure.sql"

# 2. Insertar datos en orden de dependencias
echo "2. Insertando datos..."
psql --host="$DB_HOST" --port="$DB_PORT" --username="$DB_USER" --dbname="$DB_NAME" --file="data_dependencies.sql"
psql --host="$DB_HOST" --port="$DB_PORT" --username="$DB_USER" --dbname="$DB_NAME" --file="data_subdependencies.sql"
psql --host="$DB_HOST" --port="$DB_PORT" --username="$DB_USER" --dbname="$DB_NAME" --file="data_faq_themes.sql"
psql --host="$DB_HOST" --port="$DB_PORT" --username="$DB_USER" --dbname="$DB_NAME" --file="data_procedures.sql"
psql --host="$DB_HOST" --port="$DB_PORT" --username="$DB_USER" --dbname="$DB_NAME" --file="data_opas.sql"
psql --host="$DB_HOST" --port="$DB_PORT" --username="$DB_USER" --dbname="$DB_NAME" --file="data_municipal_faqs.sql"

# 3. Aplicar políticas RLS
echo "3. Aplicando políticas RLS..."
psql --host="$DB_HOST" --port="$DB_PORT" --username="$DB_USER" --dbname="$DB_NAME" --file="03_rls_policies.sql"

# 4. Crear funciones y triggers
echo "4. Creando funciones y triggers..."
psql --host="$DB_HOST" --port="$DB_PORT" --username="$DB_USER" --dbname="$DB_NAME" --file="04_functions_triggers.sql"

echo "Restauración completada!"
EOF

chmod +x "$EXPORT_DIR/restore_database.sh"

log "✓ Script de restauración creado exitosamente"

# =============================================
# FINALIZACIÓN
# =============================================

log "🎉 Exportación completada exitosamente!"
log "📁 Archivos generados en: $EXPORT_DIR"
log "📊 Revisa el archivo 00_export_summary.md para detalles"

# Mostrar tamaño total de la exportación
total_size=$(du -sh "$EXPORT_DIR" | cut -f1)
log "💾 Tamaño total de la exportación: $total_size"

echo ""
info "Para restaurar la base de datos en otro servidor:"
info "1. Copia todo el directorio $EXPORT_DIR"
info "2. Ejecuta: cd $EXPORT_DIR && ./restore_database.sh [host] [port] [database] [user]"
echo ""
