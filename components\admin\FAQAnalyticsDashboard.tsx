'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  BarChart3, 
  Search, 
  Eye, 
  TrendingUp, 
  Users, 
  Clock,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react'
import faqAnalytics from '@/lib/services/faqAnalytics'
import type { FAQMetrics } from '@/lib/services/faqAnalytics'

/**
 * Props para el dashboard de analytics FAQ
 */
interface FAQAnalyticsDashboardProps {
  className?: string
}

/**
 * Dashboard de analytics para administradores del sistema FAQ
 */
export function FAQAnalyticsDashboard({ className = '' }: FAQAnalyticsDashboardProps) {
  const [metrics, setMetrics] = useState<FAQMetrics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d')
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())

  /**
   * Cargar métricas de analytics
   */
  const loadMetrics = async () => {
    try {
      setIsLoading(true)
      const endDate = new Date()
      const startDate = new Date()
      
      // Calcular rango de fechas
      switch (timeRange) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7)
          break
        case '30d':
          startDate.setDate(endDate.getDate() - 30)
          break
        case '90d':
          startDate.setDate(endDate.getDate() - 90)
          break
      }

      const metricsData = await faqAnalytics.getMetrics({ start: startDate, end: endDate })
      setMetrics(metricsData)
      setLastUpdated(new Date())
    } catch (error) {
      console.error('Error cargando métricas FAQ:', error)
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * Exportar datos de analytics
   */
  const exportData = async () => {
    if (!metrics) return

    const data = {
      metrics,
      exportDate: new Date().toISOString(),
      timeRange
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `faq-analytics-${timeRange}-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // Cargar datos iniciales
  useEffect(() => {
    loadMetrics()
  }, [timeRange])

  if (isLoading) {
    return (
      <div className={`faq-analytics-dashboard ${className}`}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Analytics FAQ
            </CardTitle>
            <CardDescription>Cargando métricas...</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!metrics) {
    return (
      <div className={`faq-analytics-dashboard ${className}`}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Analytics FAQ
            </CardTitle>
            <CardDescription>Error cargando métricas</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">No se pudieron cargar las métricas</p>
              <Button onClick={loadMetrics} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Reintentar
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={`faq-analytics-dashboard space-y-6 ${className}`}>
      {/* Header con controles */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Analytics FAQ
              </CardTitle>
              <CardDescription>
                Métricas de uso del sistema FAQ - Última actualización: {lastUpdated.toLocaleString()}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {/* Selector de rango temporal */}
              <div className="flex gap-1">
                {(['7d', '30d', '90d'] as const).map((range) => (
                  <Button
                    key={range}
                    variant={timeRange === range ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setTimeRange(range)}
                  >
                    {range === '7d' ? '7 días' : range === '30d' ? '30 días' : '90 días'}
                  </Button>
                ))}
              </div>
              <Button onClick={exportData} variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Exportar
              </Button>
              <Button onClick={loadMetrics} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Actualizar
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Métricas principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Search className="h-4 w-4 text-blue-500" />
              Total Búsquedas
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalSearches.toLocaleString()}</div>
            <p className="text-xs text-gray-500 mt-1">
              Tasa de éxito: {(metrics.searchSuccessRate * 100).toFixed(1)}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Eye className="h-4 w-4 text-green-500" />
              Total Visualizaciones
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalViews.toLocaleString()}</div>
            <p className="text-xs text-gray-500 mt-1">
              FAQs expandidas/leídas
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Clock className="h-4 w-4 text-orange-500" />
              Tiempo Promedio
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.averageResponseTime}ms</div>
            <p className="text-xs text-gray-500 mt-1">
              Tiempo de respuesta
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users className="h-4 w-4 text-purple-500" />
              Uso por Contexto
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics.contextUsage.reduce((sum, ctx) => sum + ctx.count, 0)}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Interacciones totales
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Términos de búsqueda más populares */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Términos de Búsqueda Populares</CardTitle>
            <CardDescription>Los términos más buscados por los usuarios</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {metrics.topSearchTerms.map((term, index) => (
                <div key={term.term} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">
                      {index + 1}
                    </Badge>
                    <span className="font-medium">{term.term}</span>
                  </div>
                  <Badge variant="secondary">{term.count} búsquedas</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">FAQs Más Consultadas</CardTitle>
            <CardDescription>Las preguntas más visualizadas</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {metrics.topFAQs.map((faq, index) => (
                <div key={faq.faqId} className="space-y-1">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">
                        {index + 1}
                      </Badge>
                      <span className="text-sm font-medium line-clamp-1">{faq.question}</span>
                    </div>
                    <Badge variant="secondary">{faq.views} vistas</Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Uso por categoría y contexto */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Categorías Populares</CardTitle>
            <CardDescription>Distribución de uso por categoría</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {metrics.topCategories.map((category, index) => (
                <div key={category.category} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">
                      {index + 1}
                    </Badge>
                    <span className="font-medium capitalize">{category.category}</span>
                  </div>
                  <Badge variant="secondary">{category.count} interacciones</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Uso por Contexto</CardTitle>
            <CardDescription>Dónde se usa más el sistema FAQ</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {metrics.contextUsage.map((context, index) => (
                <div key={context.context} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">
                      {index + 1}
                    </Badge>
                    <span className="font-medium capitalize">{context.context}</span>
                  </div>
                  <Badge variant="secondary">{context.count} usos</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default FAQAnalyticsDashboard
