# =============================================
# SCRIPT DE EXPORTACIÓN COMPLETA - SISTEMA MUNICIPAL CHÍA
# =============================================
# Exporta estructura, datos y políticas RLS de las tablas principales
# Fecha: 2025-01-06
# Autor: Sistema Municipal Chía - DBA Export
# Requisitos: PostgreSQL client tools instalados
# =============================================

param(
    [string]$DBHost = $env:SUPABASE_DB_HOST,
    [string]$DBPort = $env:SUPABASE_DB_PORT,
    [string]$DBName = $env:SUPABASE_DB_NAME,
    [string]$DBUser = $env:SUPABASE_DB_USER,
    [string]$DBPassword = $env:SUPABASE_DB_PASSWORD,
    [string]$ExportPath = ".\database_exports"
)

# Configuración por defecto si no se proporcionan variables de entorno
if (-not $DBHost) { $DBHost = "db.zeieudvbhlrlnfkwejoh.supabase.co" }
if (-not $DBPort) { $DBPort = "5432" }
if (-not $DBName) { $DBName = "postgres" }
if (-not $DBUser) { $DBUser = "postgres" }

# Funciones auxiliares
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARNING" { "Yellow" }
        "SUCCESS" { "Green" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

function Test-PostgreSQLTools {
    try {
        $null = Get-Command pg_dump -ErrorAction Stop
        $null = Get-Command psql -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

# Verificaciones iniciales
Write-Log "Iniciando exportación de base de datos Sistema Municipal Chía..." "SUCCESS"

if (-not (Test-PostgreSQLTools)) {
    Write-Log "PostgreSQL client tools no están instalados o no están en el PATH." "ERROR"
    Write-Log "Por favor instala PostgreSQL client tools desde: https://www.postgresql.org/download/" "ERROR"
    exit 1
}

if (-not $DBPassword) {
    Write-Log "La variable SUPABASE_DB_PASSWORD no está configurada." "ERROR"
    Write-Log "Por favor configura las variables de entorno de Supabase:" "ERROR"
    Write-Log "`$env:SUPABASE_DB_PASSWORD = 'tu_password'" "ERROR"
    exit 1
}

# Crear directorio de exportación
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$exportDir = Join-Path $ExportPath "export_$timestamp"
New-Item -ItemType Directory -Path $exportDir -Force | Out-Null

Write-Log "Directorio de exportación: $exportDir" "INFO"

# Configurar variable de entorno para password
$env:PGPASSWORD = $DBPassword

# Lista de tablas principales
$mainTables = @(
    "dependencies",
    "subdependencies", 
    "procedures",
    "opas",
    "faq_themes",
    "municipal_faqs"
)

# =============================================
# 1. EXPORTAR ESTRUCTURA COMPLETA
# =============================================

Write-Log "Exportando estructura completa de la base de datos..." "INFO"

$structureFile = Join-Path $exportDir "01_complete_structure.sql"
$pgDumpArgs = @(
    "--host=$DBHost",
    "--port=$DBPort",
    "--username=$DBUser",
    "--dbname=$DBName",
    "--schema-only",
    "--no-owner",
    "--no-privileges",
    "--verbose",
    "--file=$structureFile"
)

try {
    & pg_dump @pgDumpArgs
    if ($LASTEXITCODE -eq 0) {
        Write-Log "✓ Estructura completa exportada exitosamente" "SUCCESS"
    } else {
        throw "pg_dump falló con código de salida $LASTEXITCODE"
    }
}
catch {
    Write-Log "✗ Error al exportar estructura completa: $_" "ERROR"
    exit 1
}

# =============================================
# 2. EXPORTAR TABLAS PRINCIPALES CON DATOS
# =============================================

Write-Log "Exportando tablas principales con datos..." "INFO"

foreach ($table in $mainTables) {
    Write-Log "Exportando tabla: $table" "INFO"
    
    # Exportar estructura de la tabla
    $tableStructureFile = Join-Path $exportDir "structure_$table.sql"
    $pgDumpStructureArgs = @(
        "--host=$DBHost",
        "--port=$DBPort",
        "--username=$DBUser",
        "--dbname=$DBName",
        "--table=$table",
        "--schema-only",
        "--no-owner",
        "--no-privileges",
        "--file=$tableStructureFile"
    )
    
    try {
        & pg_dump @pgDumpStructureArgs
        if ($LASTEXITCODE -ne 0) {
            throw "Error exportando estructura de $table"
        }
    }
    catch {
        Write-Log "✗ Error al exportar estructura de $table`: $_" "WARNING"
        continue
    }
    
    # Exportar datos de la tabla en formato SQL
    $tableDataFile = Join-Path $exportDir "data_$table.sql"
    $pgDumpDataArgs = @(
        "--host=$DBHost",
        "--port=$DBPort",
        "--username=$DBUser",
        "--dbname=$DBName",
        "--table=$table",
        "--data-only",
        "--no-owner",
        "--no-privileges",
        "--column-inserts",
        "--file=$tableDataFile"
    )
    
    try {
        & pg_dump @pgDumpDataArgs
        if ($LASTEXITCODE -ne 0) {
            throw "Error exportando datos de $table"
        }
    }
    catch {
        Write-Log "✗ Error al exportar datos de $table`: $_" "WARNING"
        continue
    }
    
    # Exportar datos en formato CSV
    $csvFile = Join-Path $exportDir "data_$table.csv"
    $copyCommand = "\copy (SELECT * FROM $table ORDER BY created_at) TO '$csvFile' WITH CSV HEADER;"
    
    $psqlArgs = @(
        "--host=$DBHost",
        "--port=$DBPort",
        "--username=$DBUser",
        "--dbname=$DBName",
        "--command=$copyCommand"
    )
    
    try {
        & psql @psqlArgs
        if ($LASTEXITCODE -eq 0) {
            Write-Log "✓ Tabla $table exportada exitosamente" "SUCCESS"
        } else {
            throw "Error exportando CSV de $table"
        }
    }
    catch {
        Write-Log "✗ Error al exportar CSV de $table`: $_" "WARNING"
    }
}

# =============================================
# 3. EXPORTAR POLÍTICAS RLS
# =============================================

Write-Log "Exportando políticas RLS..." "INFO"

$rlsFile = Join-Path $exportDir "03_rls_policies.sql"
$rlsQuery = @"
-- =============================================
-- POLÍTICAS RLS - SISTEMA MUNICIPAL CHÍA
-- Generado automáticamente el: $(Get-Date)
-- =============================================

-- Funciones auxiliares para RLS
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS `$`$
BEGIN
  RETURN (
    SELECT r.name
    FROM profiles p
    JOIN roles r ON p.role_id = r.id
    WHERE p.id = auth.uid()
  );
END;
`$`$ LANGUAGE plpgsql SECURITY DEFINER;

-- Exportar todas las políticas RLS
SELECT 
    'ALTER TABLE ' || tablename || ' ENABLE ROW LEVEL SECURITY;' || chr(10) ||
    'CREATE POLICY "' || policyname || '" ON ' || tablename || 
    ' FOR ' || cmd || 
    CASE WHEN roles IS NOT NULL THEN ' TO ' || array_to_string(roles, ', ') ELSE '' END ||
    CASE WHEN qual IS NOT NULL THEN ' USING (' || qual || ')' ELSE '' END ||
    CASE WHEN with_check IS NOT NULL THEN ' WITH CHECK (' || with_check || ')' ELSE '' END || ';' || chr(10)
FROM pg_policies 
WHERE tablename IN ('dependencies', 'subdependencies', 'procedures', 'opas', 'faq_themes', 'municipal_faqs')
ORDER BY tablename, policyname;
"@

$psqlRlsArgs = @(
    "--host=$DBHost",
    "--port=$DBPort",
    "--username=$DBUser",
    "--dbname=$DBName",
    "--output=$rlsFile",
    "--command=$rlsQuery"
)

try {
    & psql @psqlRlsArgs
    if ($LASTEXITCODE -eq 0) {
        Write-Log "✓ Políticas RLS exportadas exitosamente" "SUCCESS"
    } else {
        throw "Error exportando políticas RLS"
    }
}
catch {
    Write-Log "✗ Error al exportar políticas RLS: $_" "WARNING"
}

# =============================================
# 4. GENERAR ESTADÍSTICAS DE EXPORTACIÓN
# =============================================

Write-Log "Generando estadísticas de exportación..." "INFO"

$summaryFile = Join-Path $exportDir "00_export_summary.md"
$summaryContent = @"
# Resumen de Exportación - Sistema Municipal Chía

**Fecha de exportación:** $(Get-Date)
**Directorio:** $exportDir

## Tablas Exportadas

"@

foreach ($table in $mainTables) {
    $countQuery = "SELECT COUNT(*) FROM $table;"
    $psqlCountArgs = @(
        "--host=$DBHost",
        "--port=$DBPort",
        "--username=$DBUser",
        "--dbname=$DBName",
        "--tuples-only",
        "--command=$countQuery"
    )
    
    try {
        $count = & psql @psqlCountArgs
        $count = $count.Trim()
        $summaryContent += "- **$table**: $count registros`n"
    }
    catch {
        $summaryContent += "- **$table**: Error obteniendo conteo`n"
    }
}

$summaryContent += @"

## Archivos Generados

- `01_complete_structure.sql` - Estructura completa de la base de datos
- `structure_[tabla].sql` - Estructura individual por tabla
- `data_[tabla].sql` - Datos en formato SQL INSERT
- `data_[tabla].csv` - Datos en formato CSV
- `03_rls_policies.sql` - Políticas de Row Level Security

## Instrucciones de Restauración

1. Crear nueva base de datos
2. Ejecutar `01_complete_structure.sql`
3. Ejecutar archivos `data_[tabla].sql` en orden de dependencias
4. Ejecutar `03_rls_policies.sql`

"@

Set-Content -Path $summaryFile -Value $summaryContent -Encoding UTF8

Write-Log "✓ Estadísticas generadas exitosamente" "SUCCESS"

# =============================================
# 5. CREAR SCRIPT DE RESTAURACIÓN
# =============================================

Write-Log "Creando script de restauración..." "INFO"

$restoreScript = Join-Path $exportDir "Restore-Database.ps1"
$restoreContent = @'
# Script de restauración para Sistema Municipal Chía
param(
    [string]$DBHost = "localhost",
    [string]$DBPort = "5432",
    [string]$DBName = "chia_municipal",
    [string]$DBUser = "postgres",
    [string]$DBPassword
)

if (-not $DBPassword) {
    $DBPassword = Read-Host "Ingresa la contraseña de la base de datos" -AsSecureString
    $DBPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($DBPassword))
}

$env:PGPASSWORD = $DBPassword

Write-Host "Restaurando base de datos Sistema Municipal Chía..."
Write-Host "Host: $DBHost`:$DBPort"
Write-Host "Database: $DBName"
Write-Host "User: $DBUser"

# 1. Crear estructura
Write-Host "1. Creando estructura..."
& psql --host=$DBHost --port=$DBPort --username=$DBUser --dbname=$DBName --file="01_complete_structure.sql"

# 2. Insertar datos en orden de dependencias
Write-Host "2. Insertando datos..."
$tables = @("dependencies", "subdependencies", "faq_themes", "procedures", "opas", "municipal_faqs")
foreach ($table in $tables) {
    Write-Host "   Insertando datos de $table..."
    & psql --host=$DBHost --port=$DBPort --username=$DBUser --dbname=$DBName --file="data_$table.sql"
}

# 3. Aplicar políticas RLS
Write-Host "3. Aplicando políticas RLS..."
& psql --host=$DBHost --port=$DBPort --username=$DBUser --dbname=$DBName --file="03_rls_policies.sql"

Write-Host "Restauración completada!"
'@

Set-Content -Path $restoreScript -Value $restoreContent -Encoding UTF8

Write-Log "✓ Script de restauración creado exitosamente" "SUCCESS"

# =============================================
# FINALIZACIÓN
# =============================================

# Limpiar variable de entorno
Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue

Write-Log "🎉 Exportación completada exitosamente!" "SUCCESS"
Write-Log "📁 Archivos generados en: $exportDir" "SUCCESS"
Write-Log "📊 Revisa el archivo 00_export_summary.md para detalles" "INFO"

# Mostrar tamaño total de la exportación
$totalSize = (Get-ChildItem -Path $exportDir -Recurse | Measure-Object -Property Length -Sum).Sum
$totalSizeMB = [math]::Round($totalSize / 1MB, 2)
Write-Log "💾 Tamaño total de la exportación: $totalSizeMB MB" "INFO"

Write-Host ""
Write-Log "Para restaurar la base de datos en otro servidor:" "INFO"
Write-Log "1. Copia todo el directorio $exportDir" "INFO"
Write-Log "2. Ejecuta: .\Restore-Database.ps1 -DBHost [host] -DBPort [port] -DBName [database] -DBUser [user]" "INFO"
Write-Host ""
