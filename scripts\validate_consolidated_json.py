#!/usr/bin/env python3
"""
=============================================
VALIDADOR DE JSON CONSOLIDADO - SISTEMA MUNICIPAL CHÍA
=============================================
Valida y optimiza el archivo JSON consolidado generado
Fecha: 2025-01-06
Autor: Sistema Municipal Chía - JSON Validator
=============================================
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Set
import logging
from datetime import datetime

class ConsolidatedJSONValidator:
    """Validador y optimizador de JSON consolidado"""
    
    def __init__(self, json_file_path: str):
        self.json_file = Path(json_file_path)
        self.setup_logging()
        self.data = None
        self.validation_errors = []
        self.validation_warnings = []
        self.optimization_suggestions = []
        
    def setup_logging(self):
        """Configurar logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
    def load_json_file(self) -> bool:
        """Cargar archivo JSON"""
        self.logger.info(f"📂 Cargando archivo JSON: {self.json_file}")
        
        if not self.json_file.exists():
            self.logger.error(f"❌ Archivo no encontrado: {self.json_file}")
            return False
            
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            
            file_size = self.json_file.stat().st_size / (1024 * 1024)
            self.logger.info(f"✅ Archivo cargado exitosamente ({file_size:.2f} MB)")
            return True
            
        except json.JSONDecodeError as e:
            self.logger.error(f"❌ Error de formato JSON: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Error cargando archivo: {e}")
            return False
            
    def validate_structure(self) -> bool:
        """Validar estructura básica del JSON"""
        self.logger.info("🔍 Validando estructura básica...")
        
        # Validar secciones principales
        required_sections = ["metadata", "dependencies"]
        for section in required_sections:
            if section not in self.data:
                self.validation_errors.append(f"Sección faltante: {section}")
        
        if self.validation_errors:
            return False
            
        # Validar metadata
        metadata = self.data["metadata"]
        required_metadata_fields = [
            "generated_at", "generator", "version", "total_dependencies",
            "total_subdependencies", "total_tramites", "total_opas", "total_faqs"
        ]
        
        for field in required_metadata_fields:
            if field not in metadata:
                self.validation_errors.append(f"Campo faltante en metadata: {field}")
        
        # Validar que dependencies sea una lista
        if not isinstance(self.data["dependencies"], list):
            self.validation_errors.append("'dependencies' debe ser una lista")
            
        return len(self.validation_errors) == 0
        
    def validate_dependencies(self) -> bool:
        """Validar estructura de dependencias"""
        self.logger.info("🏢 Validando dependencias...")
        
        required_dep_fields = [
            "id", "code", "name", "subdependencies", "counters"
        ]
        
        dependency_codes = set()
        dependency_ids = set()
        
        for i, dep in enumerate(self.data["dependencies"]):
            # Validar campos requeridos
            for field in required_dep_fields:
                if field not in dep:
                    self.validation_errors.append(f"Dependencia {i}: falta campo '{field}'")
            
            # Validar unicidad de códigos e IDs
            if "code" in dep:
                if dep["code"] in dependency_codes:
                    self.validation_errors.append(f"Código de dependencia duplicado: {dep['code']}")
                dependency_codes.add(dep["code"])
                
            if "id" in dep:
                if dep["id"] in dependency_ids:
                    self.validation_errors.append(f"ID de dependencia duplicado: {dep['id']}")
                dependency_ids.add(dep["id"])
            
            # Validar subdependencias
            if "subdependencies" in dep:
                if not isinstance(dep["subdependencies"], list):
                    self.validation_errors.append(f"Dependencia {dep.get('name', i)}: subdependencies debe ser lista")
                else:
                    self.validate_subdependencies(dep["subdependencies"], dep.get("name", f"Dep-{i}"))
        
        return len(self.validation_errors) == 0
        
    def validate_subdependencies(self, subdependencies: List[Dict], dep_name: str):
        """Validar estructura de subdependencias"""
        required_subdep_fields = [
            "id", "code", "name", "tramites", "opas", "faq_themes", "counters"
        ]
        
        subdep_codes = set()
        subdep_ids = set()
        
        for i, subdep in enumerate(subdependencies):
            # Validar campos requeridos
            for field in required_subdep_fields:
                if field not in subdep:
                    self.validation_errors.append(f"{dep_name} - Subdep {i}: falta campo '{field}'")
            
            # Validar unicidad dentro de la dependencia
            if "code" in subdep:
                if subdep["code"] in subdep_codes:
                    self.validation_errors.append(f"{dep_name}: código de subdependencia duplicado: {subdep['code']}")
                subdep_codes.add(subdep["code"])
                
            if "id" in subdep:
                if subdep["id"] in subdep_ids:
                    self.validation_errors.append(f"{dep_name}: ID de subdependencia duplicado: {subdep['id']}")
                subdep_ids.add(subdep["id"])
            
            # Validar contadores
            if "counters" in subdep:
                self.validate_counters(subdep, f"{dep_name} - {subdep.get('name', f'Subdep-{i}')}")
                
    def validate_counters(self, subdep: Dict, subdep_name: str):
        """Validar contadores de subdependencia"""
        counters = subdep["counters"]
        
        # Verificar que los contadores coincidan con los datos reales
        actual_tramites = len(subdep.get("tramites", []))
        actual_opas = len(subdep.get("opas", []))
        actual_themes = len(subdep.get("faq_themes", []))
        
        if counters.get("tramites_count", 0) != actual_tramites:
            self.validation_errors.append(f"{subdep_name}: contador de trámites incorrecto ({counters.get('tramites_count')} vs {actual_tramites})")
            
        if counters.get("opas_count", 0) != actual_opas:
            self.validation_errors.append(f"{subdep_name}: contador de OPAs incorrecto ({counters.get('opas_count')} vs {actual_opas})")
            
        if counters.get("faq_themes_count", 0) != actual_themes:
            self.validation_errors.append(f"{subdep_name}: contador de temas FAQ incorrecto ({counters.get('faq_themes_count')} vs {actual_themes})")
            
        # Validar total de FAQs
        actual_total_faqs = sum(len(theme.get("faqs", [])) for theme in subdep.get("faq_themes", []))
        if counters.get("total_faqs_count", 0) != actual_total_faqs:
            self.validation_errors.append(f"{subdep_name}: contador total de FAQs incorrecto ({counters.get('total_faqs_count')} vs {actual_total_faqs})")
            
    def validate_data_integrity(self) -> bool:
        """Validar integridad de datos"""
        self.logger.info("🔗 Validando integridad de datos...")
        
        # Validar que todos los trámites tengan campos requeridos
        for dep in self.data["dependencies"]:
            for subdep in dep.get("subdependencies", []):
                # Validar trámites
                for tramite in subdep.get("tramites", []):
                    required_tramite_fields = ["id", "name", "description"]
                    for field in required_tramite_fields:
                        if field not in tramite or not tramite[field]:
                            self.validation_warnings.append(f"Trámite {tramite.get('id', 'unknown')}: campo '{field}' vacío o faltante")
                
                # Validar OPAs
                for opa in subdep.get("opas", []):
                    required_opa_fields = ["id", "code", "description"]
                    for field in required_opa_fields:
                        if field not in opa or not opa[field]:
                            self.validation_warnings.append(f"OPA {opa.get('id', 'unknown')}: campo '{field}' vacío o faltante")
                
                # Validar FAQs
                for theme in subdep.get("faq_themes", []):
                    for faq in theme.get("faqs", []):
                        if not faq.get("question") or not faq.get("answer"):
                            self.validation_warnings.append(f"FAQ {faq.get('id', 'unknown')}: pregunta o respuesta vacía")
                        
                        # Validar que tenga información del tema
                        if "tema" not in faq:
                            self.validation_warnings.append(f"FAQ {faq.get('id', 'unknown')}: falta información del tema")
        
        return True
        
    def check_optimization_opportunities(self):
        """Identificar oportunidades de optimización"""
        self.logger.info("⚡ Identificando oportunidades de optimización...")
        
        # Verificar campos nulos o vacíos
        null_fields_count = 0
        empty_arrays_count = 0
        
        for dep in self.data["dependencies"]:
            for key, value in dep.items():
                if value is None:
                    null_fields_count += 1
                elif isinstance(value, list) and len(value) == 0:
                    empty_arrays_count += 1
            
            for subdep in dep.get("subdependencies", []):
                for key, value in subdep.items():
                    if value is None:
                        null_fields_count += 1
                    elif isinstance(value, list) and len(value) == 0:
                        empty_arrays_count += 1
        
        if null_fields_count > 0:
            self.optimization_suggestions.append(f"Se encontraron {null_fields_count} campos nulos que podrían omitirse")
            
        if empty_arrays_count > 0:
            self.optimization_suggestions.append(f"Se encontraron {empty_arrays_count} arrays vacíos que podrían omitirse")
        
        # Verificar tamaño del archivo
        file_size_mb = self.json_file.stat().st_size / (1024 * 1024)
        if file_size_mb > 10:
            self.optimization_suggestions.append(f"Archivo grande ({file_size_mb:.2f} MB) - considerar compresión o paginación")
            
    def generate_validation_report(self) -> Path:
        """Generar reporte de validación"""
        report_file = self.json_file.parent / f"validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# Reporte de Validación - JSON Consolidado Municipal\n\n")
            f.write(f"**Archivo validado:** {self.json_file.name}\n")
            f.write(f"**Fecha de validación:** {datetime.now().isoformat()}\n")
            f.write(f"**Tamaño del archivo:** {self.json_file.stat().st_size / (1024*1024):.2f} MB\n\n")
            
            # Estado general
            if not self.validation_errors:
                f.write("## ✅ Estado: VALIDACIÓN EXITOSA\n\n")
                f.write("El archivo JSON ha pasado todas las validaciones estructurales.\n\n")
            else:
                f.write("## ❌ Estado: ERRORES ENCONTRADOS\n\n")
                f.write("Se encontraron errores que requieren corrección.\n\n")
            
            # Errores
            if self.validation_errors:
                f.write("## 🚨 Errores de Validación\n\n")
                for i, error in enumerate(self.validation_errors, 1):
                    f.write(f"{i}. {error}\n")
                f.write("\n")
            
            # Advertencias
            if self.validation_warnings:
                f.write("## ⚠️ Advertencias\n\n")
                for i, warning in enumerate(self.validation_warnings, 1):
                    f.write(f"{i}. {warning}\n")
                f.write("\n")
            
            # Sugerencias de optimización
            if self.optimization_suggestions:
                f.write("## ⚡ Sugerencias de Optimización\n\n")
                for i, suggestion in enumerate(self.optimization_suggestions, 1):
                    f.write(f"{i}. {suggestion}\n")
                f.write("\n")
            
            # Estadísticas
            if self.data:
                f.write("## 📊 Estadísticas del Archivo\n\n")
                metadata = self.data.get("metadata", {})
                f.write(f"- **Dependencias:** {metadata.get('total_dependencies', 'N/A')}\n")
                f.write(f"- **Subdependencias:** {metadata.get('total_subdependencies', 'N/A')}\n")
                f.write(f"- **Trámites:** {metadata.get('total_tramites', 'N/A')}\n")
                f.write(f"- **OPAs:** {metadata.get('total_opas', 'N/A')}\n")
                f.write(f"- **FAQs:** {metadata.get('total_faqs', 'N/A')}\n")
                f.write(f"- **Total procedimientos:** {metadata.get('total_procedures', 'N/A')}\n")
        
        return report_file
        
    def create_optimized_version(self) -> Path:
        """Crear versión optimizada del JSON"""
        if self.validation_errors:
            self.logger.warning("⚠️ No se puede optimizar: hay errores de validación")
            return None
            
        self.logger.info("⚡ Creando versión optimizada...")
        
        # Crear copia de los datos para optimizar
        optimized_data = json.loads(json.dumps(self.data))
        
        # Remover campos nulos y arrays vacíos
        def clean_dict(obj):
            if isinstance(obj, dict):
                return {k: clean_dict(v) for k, v in obj.items() 
                       if v is not None and v != [] and v != ""}
            elif isinstance(obj, list):
                return [clean_dict(item) for item in obj if item is not None]
            else:
                return obj
        
        optimized_data = clean_dict(optimized_data)
        
        # Guardar versión optimizada
        optimized_file = self.json_file.parent / f"{self.json_file.stem}_optimized.json"
        
        with open(optimized_file, 'w', encoding='utf-8') as f:
            json.dump(optimized_data, f, ensure_ascii=False, indent=2, separators=(',', ':'))
        
        # Comparar tamaños
        original_size = self.json_file.stat().st_size
        optimized_size = optimized_file.stat().st_size
        reduction_percent = ((original_size - optimized_size) / original_size) * 100
        
        self.logger.info(f"✅ Versión optimizada creada: {optimized_file.name}")
        self.logger.info(f"📉 Reducción de tamaño: {reduction_percent:.1f}% ({original_size/1024/1024:.2f} MB → {optimized_size/1024/1024:.2f} MB)")
        
        return optimized_file
        
    def run_validation(self) -> bool:
        """Ejecutar validación completa"""
        self.logger.info("🔍 Iniciando validación de JSON consolidado...")
        
        # 1. Cargar archivo
        if not self.load_json_file():
            return False
        
        # 2. Validar estructura
        structure_valid = self.validate_structure()
        
        # 3. Validar dependencias
        dependencies_valid = self.validate_dependencies()
        
        # 4. Validar integridad de datos
        integrity_valid = self.validate_data_integrity()
        
        # 5. Identificar optimizaciones
        self.check_optimization_opportunities()
        
        # 6. Generar reporte
        report_file = self.generate_validation_report()
        self.logger.info(f"📋 Reporte generado: {report_file}")
        
        # 7. Crear versión optimizada si no hay errores
        if structure_valid and dependencies_valid:
            optimized_file = self.create_optimized_version()
            if optimized_file:
                self.logger.info(f"⚡ Versión optimizada: {optimized_file}")
        
        # Resultado final
        validation_passed = structure_valid and dependencies_valid and integrity_valid
        
        if validation_passed:
            self.logger.info("🎉 Validación completada exitosamente!")
            self.logger.info(f"✅ Errores: {len(self.validation_errors)}")
            self.logger.info(f"⚠️ Advertencias: {len(self.validation_warnings)}")
            self.logger.info(f"⚡ Sugerencias: {len(self.optimization_suggestions)}")
        else:
            self.logger.error("❌ Validación falló")
            self.logger.error(f"🚨 Errores encontrados: {len(self.validation_errors)}")
        
        return validation_passed

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Uso: python validate_consolidated_json.py <archivo_json>")
        print("Ejemplo: python validate_consolidated_json.py scripts/sistema_municipal_chia_consolidado.json")
        sys.exit(1)
    
    json_file_path = sys.argv[1]
    validator = ConsolidatedJSONValidator(json_file_path)
    
    success = validator.run_validation()
    sys.exit(0 if success else 1)
