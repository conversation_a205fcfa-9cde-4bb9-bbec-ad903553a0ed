#!/usr/bin/env python3
"""
=============================================
CONFIGURADOR DE ENTORNO - CONSOLIDACIÓN JSON
=============================================
Script para configurar variables de entorno necesarias para la consolidación JSON
Fecha: 2025-01-06
Autor: Sistema Municipal Chía - Environment Setup
=============================================
"""

import os
import sys
from pathlib import Path
import getpass
from typing import Dict, Optional

class EnvironmentSetup:
    """Configurador de variables de entorno para consolidación JSON"""
    
    def __init__(self):
        self.env_file = Path(".env")
        self.env_vars = {}
        
    def check_existing_env(self) -> Dict[str, Optional[str]]:
        """Verificar variables de entorno existentes"""
        required_vars = [
            'SUPABASE_DB_HOST',
            'SUPABASE_DB_PORT', 
            'SUPABASE_DB_NAME',
            'SUPABASE_DB_USER',
            'SUPABASE_DB_PASSWORD'
        ]
        
        existing_vars = {}
        for var in required_vars:
            existing_vars[var] = os.getenv(var)
            
        return existing_vars
        
    def load_env_file(self) -> Dict[str, str]:
        """Cargar variables desde archivo .env si existe"""
        env_vars = {}
        
        if self.env_file.exists():
            print(f"📁 Archivo .env encontrado: {self.env_file}")
            
            with open(self.env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key.strip()] = value.strip().strip('"\'')
        
        return env_vars
        
    def prompt_for_credentials(self) -> Dict[str, str]:
        """Solicitar credenciales al usuario"""
        print("\n🔐 Configuración de credenciales de Supabase")
        print("=" * 50)
        
        credentials = {}
        
        # Host
        default_host = "db.zeieudvbhlrlnfkwejoh.supabase.co"
        host = input(f"Host de la base de datos [{default_host}]: ").strip()
        credentials['SUPABASE_DB_HOST'] = host if host else default_host
        
        # Puerto
        default_port = "5432"
        port = input(f"Puerto [{default_port}]: ").strip()
        credentials['SUPABASE_DB_PORT'] = port if port else default_port
        
        # Base de datos
        default_db = "postgres"
        database = input(f"Nombre de la base de datos [{default_db}]: ").strip()
        credentials['SUPABASE_DB_NAME'] = database if database else default_db
        
        # Usuario
        default_user = "postgres"
        user = input(f"Usuario [{default_user}]: ").strip()
        credentials['SUPABASE_DB_USER'] = user if user else default_user
        
        # Contraseña
        password = getpass.getpass("Contraseña de la base de datos: ")
        if not password:
            print("❌ La contraseña es requerida")
            sys.exit(1)
        credentials['SUPABASE_DB_PASSWORD'] = password
        
        return credentials
        
    def test_connection(self, credentials: Dict[str, str]) -> bool:
        """Probar conexión a la base de datos"""
        print("\n🔌 Probando conexión a la base de datos...")
        
        try:
            import psycopg2
        except ImportError:
            print("❌ psycopg2 no está instalado. Instala con: pip install psycopg2-binary")
            return False
        
        try:
            conn = psycopg2.connect(
                host=credentials['SUPABASE_DB_HOST'],
                port=int(credentials['SUPABASE_DB_PORT']),
                database=credentials['SUPABASE_DB_NAME'],
                user=credentials['SUPABASE_DB_USER'],
                password=credentials['SUPABASE_DB_PASSWORD']
            )
            
            with conn.cursor() as cur:
                cur.execute("SELECT version();")
                version = cur.fetchone()[0]
                print(f"✅ Conexión exitosa!")
                print(f"📊 Versión PostgreSQL: {version}")
            
            conn.close()
            return True
            
        except psycopg2.Error as e:
            print(f"❌ Error de conexión: {e}")
            return False
        except Exception as e:
            print(f"❌ Error inesperado: {e}")
            return False
            
    def save_env_file(self, credentials: Dict[str, str]):
        """Guardar credenciales en archivo .env"""
        print(f"\n💾 Guardando configuración en {self.env_file}...")
        
        # Crear backup si existe
        if self.env_file.exists():
            backup_file = Path(f"{self.env_file}.backup")
            self.env_file.rename(backup_file)
            print(f"📋 Backup creado: {backup_file}")
        
        # Escribir nuevo archivo .env
        with open(self.env_file, 'w', encoding='utf-8') as f:
            f.write("# Configuración de base de datos Supabase\n")
            f.write("# Generado automáticamente para consolidación JSON\n")
            f.write(f"# Fecha: {os.popen('date').read().strip()}\n\n")
            
            for key, value in credentials.items():
                f.write(f'{key}="{value}"\n')
        
        print(f"✅ Configuración guardada en {self.env_file}")
        
    def setup_python_dependencies(self):
        """Verificar e instalar dependencias de Python"""
        print("\n📦 Verificando dependencias de Python...")
        
        required_packages = [
            'psycopg2-binary',
            'python-dotenv'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                if package == 'psycopg2-binary':
                    import psycopg2
                elif package == 'python-dotenv':
                    import dotenv
                print(f"✅ {package} - instalado")
            except ImportError:
                print(f"❌ {package} - faltante")
                missing_packages.append(package)
        
        if missing_packages:
            print(f"\n📥 Instalando paquetes faltantes: {', '.join(missing_packages)}")
            
            install_cmd = f"pip install {' '.join(missing_packages)}"
            print(f"Ejecutando: {install_cmd}")
            
            result = os.system(install_cmd)
            if result == 0:
                print("✅ Dependencias instaladas exitosamente")
            else:
                print("❌ Error instalando dependencias")
                print("Por favor instala manualmente:")
                for package in missing_packages:
                    print(f"  pip install {package}")
                return False
        
        return True
        
    def create_run_script(self):
        """Crear script de ejecución simplificado"""
        run_script = Path("scripts/run_consolidation.py")
        
        script_content = '''#!/usr/bin/env python3
"""
Script de ejecución simplificado para consolidación JSON
"""

import subprocess
import sys
from pathlib import Path

def main():
    scripts_dir = Path(__file__).parent
    
    print("🚀 Iniciando consolidación JSON del Sistema Municipal de Chía")
    print("=" * 60)
    
    # 1. Generar JSON consolidado
    print("\\n1️⃣ Generando JSON consolidado...")
    result = subprocess.run([
        sys.executable, 
        str(scripts_dir / "generate_consolidated_json.py")
    ])
    
    if result.returncode != 0:
        print("❌ Error en la generación del JSON")
        sys.exit(1)
    
    # 2. Validar JSON generado
    print("\\n2️⃣ Validando JSON generado...")
    json_file = scripts_dir / "sistema_municipal_chia_consolidado.json"
    
    if json_file.exists():
        result = subprocess.run([
            sys.executable,
            str(scripts_dir / "validate_consolidated_json.py"),
            str(json_file)
        ])
        
        if result.returncode == 0:
            print("\\n🎉 Consolidación completada exitosamente!")
            print(f"📁 Archivo generado: {json_file}")
            print(f"📋 Revisa los reportes en: {scripts_dir}")
        else:
            print("\\n⚠️ Consolidación completada con advertencias")
            print("📋 Revisa el reporte de validación para más detalles")
    else:
        print("❌ No se encontró el archivo JSON generado")
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
        
        with open(run_script, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # Hacer ejecutable en sistemas Unix
        if sys.platform != 'win32':
            os.chmod(run_script, 0o755)
        
        print(f"📝 Script de ejecución creado: {run_script}")
        
    def run_setup(self):
        """Ejecutar configuración completa"""
        print("🔧 Configurador de Entorno - Consolidación JSON Municipal")
        print("=" * 60)
        
        # 1. Verificar dependencias
        if not self.setup_python_dependencies():
            return False
        
        # 2. Verificar variables existentes
        existing_vars = self.check_existing_env()
        env_file_vars = self.load_env_file()
        
        # Combinar variables existentes
        all_vars = {**env_file_vars, **{k: v for k, v in existing_vars.items() if v}}
        
        # 3. Verificar si necesitamos configurar
        if all_vars.get('SUPABASE_DB_PASSWORD'):
            print("✅ Configuración existente encontrada")
            
            # Probar conexión con configuración existente
            if self.test_connection(all_vars):
                print("🎉 Configuración válida - listo para usar!")
                self.create_run_script()
                return True
            else:
                print("❌ Configuración existente no funciona")
        
        # 4. Solicitar nueva configuración
        print("🔧 Configuración requerida")
        credentials = self.prompt_for_credentials()
        
        # 5. Probar conexión
        if not self.test_connection(credentials):
            print("❌ No se pudo establecer conexión")
            retry = input("¿Intentar de nuevo? (s/N): ").lower().strip()
            if retry == 's':
                return self.run_setup()
            else:
                return False
        
        # 6. Guardar configuración
        self.save_env_file(credentials)
        
        # 7. Crear script de ejecución
        self.create_run_script()
        
        print("\n🎉 Configuración completada exitosamente!")
        print("\n📋 Próximos pasos:")
        print("1. Ejecutar: python scripts/run_consolidation.py")
        print("2. O ejecutar manualmente:")
        print("   - python scripts/generate_consolidated_json.py")
        print("   - python scripts/validate_consolidated_json.py scripts/sistema_municipal_chia_consolidado.json")
        
        return True

if __name__ == "__main__":
    setup = EnvironmentSetup()
    success = setup.run_setup()
    sys.exit(0 if success else 1)
