import { Metadata } from 'next'
import Image from 'next/image'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'Autenticación - Sistema de Atención Ciudadana Chía',
  description: 'Accede a tu cuenta del Sistema de Atención Ciudadana del Municipio de Chía',
}

interface AuthLayoutProps {
  children: React.ReactNode
}

export default function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-chia-blue-50 to-chia-green-50">
      <div className="flex min-h-screen">
        {/* Left side - Branding */}
        <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-chia-blue-600 to-chia-green-600 relative overflow-hidden">
          <div className="absolute inset-0 bg-black/20" />
          <div className="relative z-10 flex flex-col justify-center px-12 text-white">
            <div className="mb-8">
              <Link href="/" className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center">
                  <span className="text-chia-blue-600 font-bold text-xl">C</span>
                </div>
                <div>
                  <h1 className="text-2xl font-bold">Municipio de Chía</h1>
                  <p className="text-chia-blue-100">Sistema de Atención Ciudadana</p>
                </div>
              </Link>
            </div>
            
            <div className="space-y-6">
              <h2 className="text-4xl font-bold leading-tight">
                Servicios digitales para una mejor ciudadanía
              </h2>
              <p className="text-xl text-chia-blue-100 leading-relaxed">
                Accede a todos los trámites y servicios municipales desde un solo lugar. 
                Gestiona tus procedimientos de manera rápida, segura y eficiente.
              </p>
              
              <div className="space-y-4 pt-8">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-chia-green-400 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <span className="text-lg">Trámites en línea 24/7</span>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-chia-green-400 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <span className="text-lg">Seguimiento en tiempo real</span>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-chia-green-400 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <span className="text-lg">Asistente virtual inteligente</span>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-chia-green-400 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <span className="text-lg">Notificaciones automáticas</span>
                </div>
              </div>
            </div>
          </div>
          
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32" />
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/10 rounded-full translate-y-24 -translate-x-24" />
        </div>

        {/* Right side - Auth form */}
        <div className="flex-1 flex flex-col justify-center px-6 py-12 lg:px-8">
          <div className="sm:mx-auto sm:w-full sm:max-w-md">
            {/* Mobile logo */}
            <div className="lg:hidden mb-8 text-center">
              <Link href="/" className="inline-flex items-center space-x-2">
                <div className="w-10 h-10 bg-chia-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">C</span>
                </div>
                <div className="text-left">
                  <div className="text-lg font-bold text-chia-blue-900">Municipio de Chía</div>
                  <div className="text-sm text-chia-blue-600">Sistema de Atención Ciudadana</div>
                </div>
              </Link>
            </div>

            {children}
          </div>
          
          {/* Footer */}
          <div className="mt-8 text-center text-sm text-gray-600">
            <p>
              ¿Necesitas ayuda?{' '}
              <Link href="/contacto" className="text-chia-blue-600 hover:text-chia-blue-500 font-medium">
                Contáctanos
              </Link>
            </p>
            <p className="mt-2">
              © 2024 Municipio de Chía. Todos los derechos reservados.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
