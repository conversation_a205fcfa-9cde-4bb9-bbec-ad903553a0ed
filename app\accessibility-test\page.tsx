import { AccessibilityChecker } from '@/components/accessibility/AccessibilityChecker'
import { PublicLayout } from '@/components/layout/PublicLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ChiaLogoHeader } from '@/components/ui/chia-logo'
import { 
  CheckCircle, 
  AlertTriangle, 
  Eye, 
  Palette, 
  Type, 
  MousePointer,
  Shield
} from 'lucide-react'

export default function AccessibilityTestPage() {
  return (
    <PublicLayout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <ChiaLogoHeader />
            </div>
            <h1 className="text-3xl font-bold text-chia-blue-900 mb-2">
              Verificación de Accesibilidad WCAG 2.1 AA
            </h1>
            <p className="text-lg text-gray-600">
              Validación de los cambios implementados en el portal municipal de Chía
            </p>
          </div>

          {/* Accessibility Checker Component */}
          <div className="mb-8">
            <AccessibilityChecker />
          </div>

          {/* Color Palette Test */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Palette className="h-5 w-5 mr-2 text-chia-blue-600" />
                Paleta de Colores Corporativa
              </CardTitle>
              <CardDescription>
                Colores oficiales del Municipio de Chía con verificación de contraste
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {/* Chia Blue */}
                <div className="space-y-2">
                  <div className="w-full h-20 bg-chia-blue-900 rounded-lg flex items-center justify-center">
                    <span className="text-white font-medium">Chia Blue 900</span>
                  </div>
                  <p className="text-xs text-center text-gray-600">#1e3a8a</p>
                </div>
                
                <div className="space-y-2">
                  <div className="w-full h-20 bg-chia-blue-800 rounded-lg flex items-center justify-center">
                    <span className="text-white font-medium">Chia Blue 800</span>
                  </div>
                  <p className="text-xs text-center text-gray-600">#1e40af</p>
                </div>

                {/* Chia Green */}
                <div className="space-y-2">
                  <div className="w-full h-20 bg-chia-green-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-medium">Chia Green 600</span>
                  </div>
                  <p className="text-xs text-center text-gray-600">#059669</p>
                </div>

                <div className="space-y-2">
                  <div className="w-full h-20 bg-chia-green-700 rounded-lg flex items-center justify-center">
                    <span className="text-white font-medium">Chia Green 700</span>
                  </div>
                  <p className="text-xs text-center text-gray-600">#15803d</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Typography Test */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Type className="h-5 w-5 mr-2 text-chia-blue-600" />
                Tipografía y Jerarquía
              </CardTitle>
              <CardDescription>
                Verificación de legibilidad y estructura de contenido
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <h1 className="text-4xl font-bold text-chia-blue-900">Encabezado H1</h1>
              <h2 className="text-3xl font-bold text-chia-blue-800">Encabezado H2</h2>
              <h3 className="text-2xl font-semibold text-chia-blue-700">Encabezado H3</h3>
              <h4 className="text-xl font-semibold text-gray-900">Encabezado H4</h4>
              <p className="text-base text-gray-700 leading-relaxed">
                Este es un párrafo de ejemplo que demuestra la legibilidad del texto normal 
                en el portal municipal. El contraste debe cumplir con los estándares WCAG 2.1 AA.
              </p>
              <p className="text-sm text-gray-600">
                Texto secundario con menor contraste pero aún accesible.
              </p>
            </CardContent>
          </Card>

          {/* Interactive Elements Test */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <MousePointer className="h-5 w-5 mr-2 text-chia-blue-600" />
                Elementos Interactivos
              </CardTitle>
              <CardDescription>
                Botones, enlaces y controles con verificación de accesibilidad
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-4">
                <Button className="bg-chia-blue-600 hover:bg-chia-blue-700">
                  Botón Primario
                </Button>
                <Button className="bg-chia-green-600 hover:bg-chia-green-700">
                  Botón Verde
                </Button>
                <Button variant="outline" className="border-chia-blue-600 text-chia-blue-600">
                  Botón Outline
                </Button>
                <Button variant="ghost" className="text-chia-blue-600 hover:bg-chia-blue-50">
                  Botón Ghost
                </Button>
              </div>
              
              <div className="flex flex-wrap gap-2">
                <Badge className="bg-chia-blue-600">Badge Azul</Badge>
                <Badge className="bg-chia-green-600">Badge Verde</Badge>
                <Badge variant="outline" className="border-chia-blue-600 text-chia-blue-600">
                  Badge Outline
                </Badge>
                <Badge variant="secondary">Badge Secundario</Badge>
              </div>

              <div className="space-y-2">
                <a href="#" className="text-chia-blue-600 hover:text-chia-blue-800 underline">
                  Enlace con subrayado
                </a>
                <br />
                <a href="#" className="text-chia-green-600 hover:text-chia-green-800 font-medium">
                  Enlace verde sin subrayado
                </a>
              </div>
            </CardContent>
          </Card>

          {/* Status Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2 text-green-600" />
                Resumen de Cumplimiento
              </CardTitle>
              <CardDescription>
                Estado general de accesibilidad del portal
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center space-x-3 p-4 bg-green-50 rounded-lg">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                  <div>
                    <h4 className="font-semibold text-green-900">Colores</h4>
                    <p className="text-sm text-green-700">Contraste WCAG 2.1 AA</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3 p-4 bg-green-50 rounded-lg">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                  <div>
                    <h4 className="font-semibold text-green-900">Navegación</h4>
                    <p className="text-sm text-green-700">Estructura semántica</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3 p-4 bg-green-50 rounded-lg">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                  <div>
                    <h4 className="font-semibold text-green-900">Interacción</h4>
                    <p className="text-sm text-green-700">Elementos accesibles</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PublicLayout>
  )
}
