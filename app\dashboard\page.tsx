import { createClient } from '@/lib/supabase/server'
import { getCurrentUser, getUserProfile } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { ProtectedLayout } from '@/components/layout'
import { RouteGuard } from '@/components/navigation'
import {
  FileText,
  MessageSquare,
  Bell,
  User,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Calendar,
  BarChart3,
  Activity,
  Star,
  ArrowRight,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  MapPin,
  Phone,
  Mail,
  ExternalLink
} from 'lucide-react'

export default async function DashboardPage() {
  const { user, error } = await getCurrentUser()

  if (error || !user) {
    redirect('/auth/login')
  }

  const { data: profile } = await getUserProfile(user.id)

  if (!profile) {
    redirect('/auth/setup-profile')
  }

  const supabase = createClient()

  // Get user's recent procedures with enhanced data
  const { data: recentProcedures } = await supabase
    .from('citizen_procedures')
    .select(`
      *,
      procedure:procedures(
        name,
        description,
        cost,
        response_time,
        category,
        difficulty_level,
        dependency:dependencies(name, acronym)
      ),
      status:procedure_statuses(name, display_name, color, is_final)
    `)
    .eq('citizen_id', user.id)
    .order('created_at', { ascending: false })
    .limit(8)

  // Get user's unread notifications with details
  const { data: recentNotifications } = await supabase
    .from('notifications')
    .select('*')
    .eq('user_id', user.id)
    .eq('is_read', false)
    .order('created_at', { ascending: false })
    .limit(5)

  const { count: unreadNotifications } = await supabase
    .from('notifications')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', user.id)
    .eq('is_read', false)

  // Enhanced procedure statistics
  const { count: totalProcedures } = await supabase
    .from('citizen_procedures')
    .select('*', { count: 'exact', head: true })
    .eq('citizen_id', user.id)

  const { count: completedProcedures } = await supabase
    .from('citizen_procedures')
    .select('*', { count: 'exact', head: true })
    .eq('citizen_id', user.id)
    .in('status_id',
      supabase
        .from('procedure_statuses')
        .select('id')
        .eq('is_final', true)
    )

  const { count: pendingProcedures } = await supabase
    .from('citizen_procedures')
    .select('*', { count: 'exact', head: true })
    .eq('citizen_id', user.id)
    .in('status_id',
      supabase
        .from('procedure_statuses')
        .select('id')
        .eq('is_final', false)
    )

  // Get procedures with upcoming deadlines
  const { data: upcomingDeadlines } = await supabase
    .from('citizen_procedures')
    .select(`
      *,
      procedure:procedures(name),
      status:procedure_statuses(display_name, color)
    `)
    .eq('citizen_id', user.id)
    .not('estimated_completion', 'is', null)
    .gte('estimated_completion', new Date().toISOString().split('T')[0])
    .lte('estimated_completion', new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0])
    .order('estimated_completion', { ascending: true })
    .limit(5)

  // Get popular procedures for recommendations
  const { data: popularProcedures } = await supabase
    .from('procedures')
    .select(`
      *,
      dependency:dependencies(name, acronym)
    `)
    .eq('is_active', true)
    .order('popularity_score', { ascending: false })
    .limit(6)

  // Calculate completion rate
  const completionRate = totalProcedures > 0 ? Math.round((completedProcedures / totalProcedures) * 100) : 0

  return (
    <ProtectedLayout requireAuth={true} allowedRoles={['ciudadano', 'admin', 'super_admin']}>
      <RouteGuard requiredRoles={['ciudadano', 'admin', 'super_admin']}>
        {/* Enhanced Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white shadow-lg">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-8">
              <div>
                <h1 className="text-3xl font-bold">
                  Bienvenido, {profile.full_name}
                </h1>
                <p className="text-blue-100 mt-1">
                  Portal Ciudadano - Municipio de Chía
                </p>
                <div className="flex items-center mt-3 space-x-4 text-sm">
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-1" />
                    {profile.city || 'Chía'}, {profile.department || 'Cundinamarca'}
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    Último acceso: {profile.last_login ? new Date(profile.last_login).toLocaleDateString() : 'Primer acceso'}
                  </div>
                </div>
              </div>
              <div className="hidden md:flex items-center space-x-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{totalProcedures || 0}</div>
                  <div className="text-xs text-blue-200">Trámites</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{completionRate}%</div>
                  <div className="text-xs text-blue-200">Completados</div>
                </div>
                <Button variant="secondary" size="sm" className="bg-white/10 hover:bg-white/20 text-white border-white/20">
                  <User className="h-4 w-4 mr-2" />
                  Mi Perfil
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Enhanced Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Trámites</CardTitle>
              <FileText className="h-5 w-5 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-600">{totalProcedures || 0}</div>
              <p className="text-xs text-muted-foreground mt-1">
                Trámites iniciados
              </p>
              <div className="mt-2">
                <Progress value={completionRate} className="h-2" />
                <p className="text-xs text-muted-foreground mt-1">
                  {completionRate}% completados
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-yellow-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">En Proceso</CardTitle>
              <Clock className="h-5 w-5 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-yellow-600">{pendingProcedures || 0}</div>
              <p className="text-xs text-muted-foreground mt-1">
                Pendientes de completar
              </p>
              {upcomingDeadlines && upcomingDeadlines.length > 0 && (
                <div className="mt-2">
                  <Badge variant="outline" className="text-xs">
                    <Calendar className="h-3 w-3 mr-1" />
                    {upcomingDeadlines.length} próximos vencimientos
                  </Badge>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-green-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completados</CardTitle>
              <CheckCircle className="h-5 w-5 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-600">{completedProcedures || 0}</div>
              <p className="text-xs text-muted-foreground mt-1">
                Trámites finalizados
              </p>
              <div className="mt-2">
                <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  Excelente progreso
                </Badge>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-purple-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Notificaciones</CardTitle>
              <Bell className="h-5 w-5 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-600">{unreadNotifications || 0}</div>
              <p className="text-xs text-muted-foreground mt-1">
                Sin leer
              </p>
              {recentNotifications && recentNotifications.length > 0 && (
                <div className="mt-2">
                  <Badge variant="outline" className="text-xs">
                    <Activity className="h-3 w-3 mr-1" />
                    Nuevas actualizaciones
                  </Badge>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Quick Actions and Widgets */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* Quick Actions */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Star className="mr-2 h-5 w-5 text-yellow-500" />
                Acciones Rápidas
              </CardTitle>
              <CardDescription>
                Servicios más utilizados
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start" variant="outline" size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Iniciar Nuevo Trámite
                <ArrowRight className="ml-auto h-4 w-4" />
              </Button>
              <Button className="w-full justify-start" variant="outline" size="sm">
                <MessageSquare className="mr-2 h-4 w-4" />
                Consultar con IA
                <ArrowRight className="ml-auto h-4 w-4" />
              </Button>
              <Button className="w-full justify-start" variant="outline" size="sm">
                <Search className="mr-2 h-4 w-4" />
                Buscar Trámites
                <ArrowRight className="ml-auto h-4 w-4" />
              </Button>
              <Button className="w-full justify-start" variant="outline" size="sm">
                <Download className="mr-2 h-4 w-4" />
                Descargar Documentos
                <ArrowRight className="ml-auto h-4 w-4" />
              </Button>
              <Button className="w-full justify-start" variant="outline" size="sm">
                <Bell className="mr-2 h-4 w-4" />
                Ver Notificaciones
                <ArrowRight className="ml-auto h-4 w-4" />
              </Button>
            </CardContent>
          </Card>

          {/* Recent Notifications */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Bell className="mr-2 h-5 w-5 text-blue-500" />
                  Notificaciones
                </div>
                {unreadNotifications > 0 && (
                  <Badge variant="destructive" className="text-xs">
                    {unreadNotifications}
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                Últimas actualizaciones
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recentNotifications && recentNotifications.length > 0 ? (
                <div className="space-y-3">
                  {recentNotifications.slice(0, 4).map((notification: any) => (
                    <div key={notification.id} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                      <div className="flex-shrink-0">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {notification.title}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {notification.message.substring(0, 60)}...
                        </p>
                        <p className="text-xs text-blue-600 mt-1">
                          {new Date(notification.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                  <Button variant="ghost" size="sm" className="w-full">
                    Ver todas las notificaciones
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <div className="text-center py-6 text-gray-500">
                  <Bell className="mx-auto h-8 w-8 text-gray-300 mb-2" />
                  <p className="text-sm">No hay notificaciones nuevas</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Upcoming Deadlines */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="mr-2 h-5 w-5 text-orange-500" />
                Próximos Vencimientos
              </CardTitle>
              <CardDescription>
                Trámites con fechas límite
              </CardDescription>
            </CardHeader>
            <CardContent>
              {upcomingDeadlines && upcomingDeadlines.length > 0 ? (
                <div className="space-y-3">
                  {upcomingDeadlines.map((procedure: any) => (
                    <div key={procedure.id} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                      <div className="flex-1">
                        <p className="font-medium text-sm text-gray-900">
                          {procedure.procedure?.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          Ref: {procedure.reference_number}
                        </p>
                        <p className="text-xs text-orange-600 mt-1">
                          Vence: {new Date(procedure.estimated_completion).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex-shrink-0">
                        <Badge variant="outline" className="text-orange-600 border-orange-200">
                          {Math.ceil((new Date(procedure.estimated_completion).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} días
                        </Badge>
                      </div>
                    </div>
                  ))}
                  <Button variant="ghost" size="sm" className="w-full">
                    Ver todos los vencimientos
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <div className="text-center py-6 text-gray-500">
                  <Calendar className="mx-auto h-8 w-8 text-gray-300 mb-2" />
                  <p className="text-sm">No hay vencimientos próximos</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Recent Procedures and Popular Procedures */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Activity className="mr-2 h-5 w-5 text-green-500" />
                  Mis Trámites Recientes
                </div>
                <Button variant="ghost" size="sm">
                  <Eye className="h-4 w-4 mr-2" />
                  Ver todos
                </Button>
              </CardTitle>
              <CardDescription>
                Últimos trámites iniciados
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recentProcedures && recentProcedures.length > 0 ? (
                <div className="space-y-4">
                  {recentProcedures.slice(0, 5).map((procedure: any) => (
                    <div key={procedure.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <p className="font-medium text-sm">
                            {procedure.procedure?.name || 'Trámite'}
                          </p>
                          {procedure.procedure?.difficulty_level && (
                            <Badge variant="outline" className="text-xs">
                              Nivel {procedure.procedure.difficulty_level}
                            </Badge>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Ref: {procedure.reference_number}
                        </p>
                        <p className="text-xs text-gray-500">
                          {procedure.procedure?.dependency?.name}
                        </p>
                        <p className="text-xs text-gray-400">
                          {new Date(procedure.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge
                          variant="outline"
                          className="text-xs"
                        >
                          {procedure.status?.display_name || 'Pendiente'}
                        </Badge>
                        <Button variant="ghost" size="sm">
                          <ArrowRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FileText className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                  <p>No tienes trámites iniciados</p>
                  <Button className="mt-4" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Iniciar Primer Trámite
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Popular Procedures Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="mr-2 h-5 w-5 text-purple-500" />
                  Trámites Populares
                </div>
                <Button variant="ghost" size="sm">
                  <Search className="h-4 w-4 mr-2" />
                  Ver todos
                </Button>
              </CardTitle>
              <CardDescription>
                Trámites más solicitados por los ciudadanos
              </CardDescription>
            </CardHeader>
            <CardContent>
              {popularProcedures && popularProcedures.length > 0 ? (
                <div className="space-y-3">
                  {popularProcedures.slice(0, 5).map((procedure: any) => (
                    <div key={procedure.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-purple-50 transition-colors cursor-pointer">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <p className="font-medium text-sm">
                            {procedure.name}
                          </p>
                          <Badge variant="secondary" className="text-xs bg-purple-100 text-purple-800">
                            Popular
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          {procedure.dependency?.name}
                        </p>
                        <div className="flex items-center space-x-4 mt-2">
                          {procedure.cost && (
                            <span className="text-xs text-green-600">
                              ${procedure.cost.toLocaleString()}
                            </span>
                          )}
                          {procedure.response_time && (
                            <span className="text-xs text-blue-600">
                              {procedure.response_time}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm">
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 text-gray-500">
                  <TrendingUp className="mx-auto h-8 w-8 text-gray-300 mb-2" />
                  <p className="text-sm">No hay datos de popularidad disponibles</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Information Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertCircle className="mr-2 h-5 w-5 text-blue-600" />
                Información Importante
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm font-medium text-blue-900">
                    Horarios de Atención Presencial
                  </p>
                  <p className="text-sm text-blue-700">
                    Lunes a Viernes: 8:00 AM - 5:00 PM
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    Sábados: 8:00 AM - 12:00 PM
                  </p>
                </div>
                <div className="p-3 bg-green-50 rounded-lg">
                  <p className="text-sm font-medium text-green-900">
                    Servicios Digitales 24/7
                  </p>
                  <p className="text-sm text-green-700">
                    Portal ciudadano y chatbot disponibles siempre
                  </p>
                </div>
                <div className="p-3 bg-yellow-50 rounded-lg">
                  <p className="text-sm font-medium text-yellow-900">
                    Días Festivos
                  </p>
                  <p className="text-sm text-yellow-700">
                    Solo servicios digitales disponibles
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-green-500">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Phone className="mr-2 h-5 w-5 text-green-600" />
                Contacto y Soporte
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-green-600" />
                  <div>
                    <p className="text-sm font-medium">Línea de Atención</p>
                    <p className="text-sm text-gray-600">(*************</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium">Email</p>
                    <p className="text-sm text-gray-600"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <MapPin className="h-4 w-4 text-red-600" />
                  <div>
                    <p className="text-sm font-medium">Dirección</p>
                    <p className="text-sm text-gray-600">
                      Carrera 11 # 17-25, Chía, Cundinamarca
                    </p>
                  </div>
                </div>
                <Button variant="outline" size="sm" className="w-full">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Ver en Google Maps
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-purple-500">
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="mr-2 h-5 w-5 text-purple-600" />
                Mi Progreso
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Trámites Completados</span>
                    <span className="text-sm text-gray-600">{completionRate}%</span>
                  </div>
                  <Progress value={completionRate} className="h-2" />
                </div>
                <div className="grid grid-cols-2 gap-4 pt-2">
                  <div className="text-center">
                    <div className="text-lg font-bold text-purple-600">{totalProcedures || 0}</div>
                    <div className="text-xs text-gray-500">Total</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-600">{completedProcedures || 0}</div>
                    <div className="text-xs text-gray-500">Completados</div>
                  </div>
                </div>
                <Badge variant="secondary" className="w-full justify-center bg-purple-100 text-purple-800">
                  <Star className="h-3 w-3 mr-1" />
                  {completionRate >= 80 ? 'Usuario Experto' : completionRate >= 50 ? 'Usuario Activo' : 'Usuario Nuevo'}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
        </div>
      </RouteGuard>
    </ProtectedLayout>
  )
}
