'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { 
  FileText,
  Image,
  File,
  Calendar,
  TrendingUp,
  <PERSON><PERSON>hart,
  Bar<PERSON>hart3,
  HardDrive
} from 'lucide-react'

interface Document {
  id: string
  name: string
  type: string
  uploadedAt: string
  procedureName?: string
  dependency?: string
}

interface DocumentStatsProps {
  documents: Document[]
  procedures: any[]
  statistics: {
    totalDocuments: number
    proceduresWithDocuments: number
    totalStorageUsed: number
    storageLimit: number
  }
}

export function DocumentStats({ documents, procedures, statistics }: DocumentStatsProps) {
  // Calculate document type distribution
  const typeDistribution = documents.reduce((acc, doc) => {
    acc[doc.type] = (acc[doc.type] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // Calculate monthly upload trends (last 6 months)
  const monthlyUploads = documents.reduce((acc, doc) => {
    const date = new Date(doc.uploadedAt)
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
    acc[monthKey] = (acc[monthKey] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // Get last 6 months
  const last6Months = Array.from({ length: 6 }, (_, i) => {
    const date = new Date()
    date.setMonth(date.getMonth() - i)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
  }).reverse()

  const monthlyData = last6Months.map(month => ({
    month: new Date(month + '-01').toLocaleDateString('es-CO', { month: 'short' }),
    uploads: monthlyUploads[month] || 0
  }))

  // Calculate dependency distribution
  const dependencyDistribution = documents.reduce((acc, doc) => {
    if (doc.dependency) {
      acc[doc.dependency] = (acc[doc.dependency] || 0) + 1
    }
    return acc
  }, {} as Record<string, number>)

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const storageUsagePercentage = (statistics.totalStorageUsed / statistics.storageLimit) * 100

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return <FileText className="h-5 w-5 text-red-600" />
      case 'image':
        return <Image className="h-5 w-5 text-green-600" />
      default:
        return <File className="h-5 w-5 text-primary" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'pdf':
        return 'bg-red-500'
      case 'image':
        return 'bg-green-500'
      case 'document':
        return 'bg-primary'
      default:
        return 'bg-gray-500'
    }
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Documentos</p>
                <p className="text-2xl font-bold text-gray-900">{statistics.totalDocuments}</p>
              </div>
              <FileText className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Trámites Activos</p>
                <p className="text-2xl font-bold text-gray-900">{statistics.proceduresWithDocuments}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Almacenamiento</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatFileSize(statistics.totalStorageUsed)}
                </p>
                <Progress value={storageUsagePercentage} className="mt-2" />
              </div>
              <HardDrive className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Este Mes</p>
                <p className="text-2xl font-bold text-gray-900">
                  {monthlyData[monthlyData.length - 1]?.uploads || 0}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Document Types Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChart className="h-5 w-5 mr-2 text-primary" />
              Distribución por Tipo
            </CardTitle>
            <CardDescription>
              Tipos de documentos más utilizados
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(typeDistribution)
                .sort(([,a], [,b]) => b - a)
                .map(([type, count]) => {
                  const percentage = (count / statistics.totalDocuments) * 100
                  return (
                    <div key={type} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {getTypeIcon(type)}
                          <span className="text-sm font-medium capitalize">
                            {type === 'pdf' ? 'PDFs' : 
                             type === 'image' ? 'Imágenes' : 
                             type === 'document' ? 'Documentos' : type}
                          </span>
                        </div>
                        <div className="text-sm text-gray-600">
                          {count} ({percentage.toFixed(1)}%)
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${getTypeColor(type)}`}
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                    </div>
                  )
                })}
            </div>
          </CardContent>
        </Card>

        {/* Monthly Upload Trend */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2 text-green-600" />
              Tendencia de Subidas
            </CardTitle>
            <CardDescription>
              Documentos subidos en los últimos 6 meses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {monthlyData.map((data, index) => {
                const maxUploads = Math.max(...monthlyData.map(d => d.uploads))
                const percentage = maxUploads > 0 ? (data.uploads / maxUploads) * 100 : 0
                
                return (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{data.month}</span>
                      <span className="text-sm text-gray-600">{data.uploads}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Dependencies Distribution */}
      {Object.keys(dependencyDistribution).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2 text-purple-600" />
              Distribución por Dependencia
            </CardTitle>
            <CardDescription>
              Documentos por dependencia municipal
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(dependencyDistribution)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 8) // Show top 8 dependencies
                .map(([dependency, count]) => {
                  const percentage = (count / statistics.totalDocuments) * 100
                  return (
                    <div key={dependency} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium truncate" title={dependency}>
                          {dependency}
                        </span>
                        <span className="text-sm text-gray-600">
                          {count} ({percentage.toFixed(1)}%)
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-purple-500 h-2 rounded-full"
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                    </div>
                  )
                })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Storage Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <HardDrive className="h-5 w-5 mr-2 text-gray-600" />
            Detalles de Almacenamiento
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">
                {formatFileSize(statistics.totalStorageUsed)}
              </p>
              <p className="text-sm text-gray-600">Usado</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">
                {formatFileSize(statistics.storageLimit - statistics.totalStorageUsed)}
              </p>
              <p className="text-sm text-gray-600">Disponible</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">
                {formatFileSize(statistics.storageLimit)}
              </p>
              <p className="text-sm text-gray-600">Total</p>
            </div>
          </div>
          <div className="mt-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Uso del almacenamiento</span>
              <span className="text-sm text-gray-600">{storageUsagePercentage.toFixed(1)}%</span>
            </div>
            <Progress value={storageUsagePercentage} className="h-3" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
