'use client'

import React from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>ead<PERSON>, ChiaLogoFooter } from '@/components/ui/chia-logo'
import {
  Building2,
  MessageCircle,
  CheckCircle,
  MapPin,
  Phone,
  Mail
} from 'lucide-react'

interface PublicLayoutProps {
  children: React.ReactNode
  className?: string
}

export function PublicLayout({ children, className }: PublicLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-chia-blue-50 via-white to-chia-green-50">
      {/* Navigation Header */}
      <nav className="bg-white/90 backdrop-blur-sm shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo and Brand */}
            <Link href="/" className="flex items-center space-x-3">
              <ChiaLogoHeader />
              <div className="hidden sm:block">
                <p className="text-xs text-primary">Sistema de Atención Ciudadana</p>
              </div>
            </Link>

            {/* Navigation Links */}
            <div className="hidden md:flex items-center space-x-6">
              <Link href="/dependencias" className="text-gray-700 hover:text-primary font-medium">
                Dependencias
              </Link>
              <Link href="/consulta-tramites" className="text-gray-700 hover:text-primary font-medium">
                Trámites
              </Link>
              <Link
                href="https://pacochia.gov.co/realice-sus-peticiones-quejas-reclamos-sugerencias-y-denuncias/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-700 hover:text-primary font-medium"
              >
                Radicar PQRS
              </Link>
              <Link href="/auth/login">
                <Button variant="outline" className="border-primary text-primary hover:bg-primary/5">
                  Iniciar Sesión
                </Button>
              </Link>
              <Link href="/auth/register">
                <Button>
                  Registrarse
                </Button>
              </Link>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button variant="ghost" size="sm">
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className={className}>
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-3 mb-4">
                <ChiaLogoFooter />
              </div>
              <p className="text-gray-400 mb-4">
                Transformando la gestión municipal a través de la tecnología para brindar
                mejores servicios a nuestros ciudadanos.
              </p>
              <div className="flex items-center space-x-2 text-sm text-gray-400">
                <CheckCircle className="h-4 w-4 text-chia-green-500" />
                <span>Gobierno Digital Certificado</span>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Enlaces Rápidos</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/consulta-tramites" className="hover:text-white">Consultar Trámites</Link></li>
                <li><Link href="/dependencias" className="hover:text-white">Dependencias</Link></li>
                <li><Link href="https://pacochia.gov.co/realice-sus-peticiones-quejas-reclamos-sugerencias-y-denuncias/" target="_blank" rel="noopener noreferrer" className="hover:text-white">Radicar PQRS</Link></li>
                <li><Link href="/auth/register" className="hover:text-white">Crear Cuenta</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Contacto</h4>
              <ul className="space-y-2 text-gray-400">
                <li className="flex items-center space-x-2">
                  <Phone className="h-4 w-4" />
                  <span>(*************</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Mail className="h-4 w-4" />
                  <span><EMAIL></span>
                </li>
                <li className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4" />
                  <span>Carrera 11 # 17-25, Chía</span>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Municipio de Chía. Todos los derechos reservados.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
