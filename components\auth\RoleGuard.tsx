'use client'

import { ReactNode } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useRole } from '@/hooks/useRole'
import { usePermissions } from '@/hooks/usePermissions'

type RoleName = 'ciudadano' | 'admin' | 'super_admin'

interface RoleGuardProps {
  children: ReactNode
  roles?: RoleName[]
  permissions?: string[]
  dependencyId?: string
  fallback?: ReactNode
  requireAll?: boolean // If true, user must have ALL specified roles/permissions
}

export function RoleGuard({
  children,
  roles = [],
  permissions = [],
  dependencyId,
  fallback = null,
  requireAll = false,
}: RoleGuardProps) {
  const { isAuthenticated } = useAuth()
  const { hasRole, hasAnyRole, isDependencyAdmin } = useRole()
  const permissionChecks = usePermissions()

  // If not authenticated, don't show content
  if (!isAuthenticated) {
    return <>{fallback}</>
  }

  // Check role requirements
  if (roles.length > 0) {
    const hasRequiredRoles = requireAll
      ? roles.every(role => hasRole(role))
      : hasAnyRole(roles)

    if (!hasRequiredRoles) {
      return <>{fallback}</>
    }
  }

  // Check permission requirements
  if (permissions.length > 0) {
    const checkPermission = (permission: string): boolean => {
      const permissionMethod = permissionChecks[permission as keyof typeof permissionChecks]
      if (typeof permissionMethod === 'function') {
        // If dependencyId is provided, pass it to the permission check
        if (dependencyId && permissionMethod.length > 0) {
          return permissionMethod(dependencyId)
        }
        return permissionMethod()
      }
      return false
    }

    const hasRequiredPermissions = requireAll
      ? permissions.every(checkPermission)
      : permissions.some(checkPermission)

    if (!hasRequiredPermissions) {
      return <>{fallback}</>
    }
  }

  // All checks passed, render children
  return <>{children}</>
}

// Convenience components for common role checks
interface ConditionalRoleProps {
  children: ReactNode
  fallback?: ReactNode
  dependencyId?: string
}

export function ShowForCitizen({ children, fallback = null }: ConditionalRoleProps) {
  return (
    <RoleGuard roles={['ciudadano']} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

export function ShowForAdmin({ children, fallback = null, dependencyId }: ConditionalRoleProps) {
  return (
    <RoleGuard roles={['admin']} fallback={fallback} dependencyId={dependencyId}>
      {children}
    </RoleGuard>
  )
}

export function ShowForSuperAdmin({ children, fallback = null }: ConditionalRoleProps) {
  return (
    <RoleGuard roles={['super_admin']} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

export function ShowForAdminOrSuperAdmin({ children, fallback = null, dependencyId }: ConditionalRoleProps) {
  return (
    <RoleGuard roles={['admin', 'super_admin']} fallback={fallback} dependencyId={dependencyId}>
      {children}
    </RoleGuard>
  )
}

export function HideForCitizen({ children, fallback = null }: ConditionalRoleProps) {
  return (
    <RoleGuard roles={['admin', 'super_admin']} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

// Permission-based components
interface PermissionGuardProps {
  children: ReactNode
  permission: string
  dependencyId?: string
  fallback?: ReactNode
}

export function ShowWithPermission({ 
  children, 
  permission, 
  dependencyId, 
  fallback = null 
}: PermissionGuardProps) {
  return (
    <RoleGuard permissions={[permission]} dependencyId={dependencyId} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

// Dependency-specific guards
export function ShowForDependencyAdmin({ 
  children, 
  dependencyId, 
  fallback = null 
}: Required<ConditionalRoleProps>) {
  const { isDependencyAdmin } = useRole()
  
  if (!isDependencyAdmin(dependencyId)) {
    return <>{fallback}</>
  }
  
  return <>{children}</>
}

// Complex conditional rendering
interface ConditionalContentProps {
  children: ReactNode
  citizenContent?: ReactNode
  adminContent?: ReactNode
  superAdminContent?: ReactNode
  dependencyId?: string
}

export function ConditionalContent({
  children,
  citizenContent,
  adminContent,
  superAdminContent,
  dependencyId,
}: ConditionalContentProps) {
  const { isCitizen, isAdmin, isSuperAdmin, isDependencyAdmin } = useRole()

  // Super admin content takes priority
  if (isSuperAdmin && superAdminContent) {
    return <>{superAdminContent}</>
  }

  // Admin content for dependency admins
  if (isAdmin && adminContent) {
    if (dependencyId && isDependencyAdmin(dependencyId)) {
      return <>{adminContent}</>
    } else if (!dependencyId) {
      return <>{adminContent}</>
    }
  }

  // Citizen content
  if (isCitizen && citizenContent) {
    return <>{citizenContent}</>
  }

  // Default content
  return <>{children}</>
}
