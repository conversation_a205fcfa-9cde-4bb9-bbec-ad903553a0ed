#!/usr/bin/env python3
"""
=============================================
VALIDADOR DE EXPORTACIÓN - SISTEMA MUNICIPAL CHÍA
=============================================
Valida la integridad de los datos exportados
Fecha: 2025-01-06
Autor: Sistema Municipal Chía - DBA Validator
=============================================
"""

import os
import sys
import json
import csv
from pathlib import Path
from typing import Dict, List, Any
import logging

try:
    import psycopg2
    from psycopg2.extras import RealDictCursor
except ImportError:
    print("Error: psycopg2 no está instalado. Instala con: pip install psycopg2-binary")
    sys.exit(1)

class ExportValidator:
    """Validador de exportación de base de datos municipal"""
    
    def __init__(self, export_dir: str):
        self.export_dir = Path(export_dir)
        self.setup_logging()
        self.setup_config()
        
        # Tablas principales
        self.main_tables = [
            "dependencies",
            "subdependencies", 
            "procedures",
            "opas",
            "faq_themes",
            "municipal_faqs"
        ]
        
        # Contadores de validación
        self.validation_results = {
            'files_checked': 0,
            'files_missing': 0,
            'data_integrity_passed': 0,
            'data_integrity_failed': 0,
            'total_records_original': 0,
            'total_records_exported': 0
        }
        
    def setup_logging(self):
        """Configurar logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_config(self):
        """Configurar conexión a la base de datos original"""
        self.db_config = {
            'host': os.getenv('SUPABASE_DB_HOST', 'db.zeieudvbhlrlnfkwejoh.supabase.co'),
            'port': int(os.getenv('SUPABASE_DB_PORT', '5432')),
            'database': os.getenv('SUPABASE_DB_NAME', 'postgres'),
            'user': os.getenv('SUPABASE_DB_USER', 'postgres'),
            'password': os.getenv('SUPABASE_DB_PASSWORD')
        }
        
    def get_connection(self):
        """Obtener conexión a la base de datos original"""
        try:
            conn = psycopg2.connect(**self.db_config)
            return conn
        except psycopg2.Error as e:
            self.logger.error(f"Error conectando a la base de datos: {e}")
            return None
            
    def validate_export_directory(self) -> bool:
        """Validar que el directorio de exportación existe y tiene los archivos esperados"""
        self.logger.info(f"Validando directorio de exportación: {self.export_dir}")
        
        if not self.export_dir.exists():
            self.logger.error(f"El directorio de exportación no existe: {self.export_dir}")
            return False
            
        # Archivos esperados
        expected_files = [
            "00_export_summary.md",
            "01_complete_structure.sql",
            "03_rls_policies.sql"
        ]
        
        # Archivos por tabla
        for table in self.main_tables:
            expected_files.extend([
                f"structure_{table}.sql",
                f"data_{table}.sql",
                f"data_{table}.csv"
            ])
            
        missing_files = []
        for file_name in expected_files:
            file_path = self.export_dir / file_name
            if file_path.exists():
                self.validation_results['files_checked'] += 1
                self.logger.info(f"✓ Archivo encontrado: {file_name}")
            else:
                missing_files.append(file_name)
                self.validation_results['files_missing'] += 1
                self.logger.warning(f"✗ Archivo faltante: {file_name}")
                
        if missing_files:
            self.logger.error(f"Archivos faltantes: {len(missing_files)}")
            return False
            
        self.logger.info(f"✓ Todos los archivos esperados están presentes ({len(expected_files)} archivos)")
        return True
        
    def validate_csv_integrity(self, table_name: str) -> bool:
        """Validar integridad de archivo CSV"""
        csv_file = self.export_dir / f"data_{table_name}.csv"
        
        if not csv_file.exists():
            self.logger.error(f"Archivo CSV no encontrado: {csv_file}")
            return False
            
        try:
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
                
            if not rows:
                self.logger.warning(f"Archivo CSV vacío: {table_name}")
                return True
                
            # Validar que todas las filas tienen el mismo número de columnas
            expected_columns = len(reader.fieldnames)
            for i, row in enumerate(rows):
                if len(row) != expected_columns:
                    self.logger.error(f"Fila {i+1} en {table_name} tiene número incorrecto de columnas")
                    return False
                    
            self.logger.info(f"✓ CSV válido: {table_name} ({len(rows)} filas, {expected_columns} columnas)")
            return True
            
        except Exception as e:
            self.logger.error(f"Error validando CSV {table_name}: {e}")
            return False
            
    def validate_sql_syntax(self, table_name: str) -> bool:
        """Validar sintaxis básica de archivo SQL"""
        sql_file = self.export_dir / f"data_{table_name}.sql"
        
        if not sql_file.exists():
            self.logger.error(f"Archivo SQL no encontrado: {sql_file}")
            return False
            
        try:
            with open(sql_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Validaciones básicas
            if not content.strip():
                self.logger.warning(f"Archivo SQL vacío: {table_name}")
                return True
                
            # Contar statements INSERT
            insert_count = content.count('INSERT INTO')
            if insert_count == 0:
                self.logger.warning(f"No se encontraron statements INSERT en {table_name}")
                return True
                
            # Verificar que cada INSERT termina con punto y coma
            lines = content.split('\n')
            insert_lines = [line for line in lines if line.strip().startswith('INSERT INTO')]
            
            for line in insert_lines:
                if not line.strip().endswith(';'):
                    self.logger.error(f"Statement INSERT sin punto y coma en {table_name}: {line[:50]}...")
                    return False
                    
            self.logger.info(f"✓ SQL válido: {table_name} ({insert_count} statements INSERT)")
            return True
            
        except Exception as e:
            self.logger.error(f"Error validando SQL {table_name}: {e}")
            return False
            
    def compare_record_counts(self) -> bool:
        """Comparar conteos de registros entre base de datos original y archivos exportados"""
        self.logger.info("Comparando conteos de registros...")
        
        conn = self.get_connection()
        if not conn:
            self.logger.warning("No se puede conectar a la base de datos original para comparar conteos")
            return True
            
        all_counts_match = True
        
        try:
            with conn.cursor() as cur:
                for table in self.main_tables:
                    # Contar registros en base de datos original
                    try:
                        cur.execute(f"SELECT COUNT(*) FROM {table}")
                        original_count = cur.fetchone()[0]
                        self.validation_results['total_records_original'] += original_count
                    except psycopg2.Error as e:
                        self.logger.warning(f"Error contando registros originales en {table}: {e}")
                        continue
                    
                    # Contar registros en archivo CSV exportado
                    csv_file = self.export_dir / f"data_{table}.csv"
                    if csv_file.exists():
                        try:
                            with open(csv_file, 'r', encoding='utf-8') as f:
                                reader = csv.reader(f)
                                next(reader)  # Skip header
                                exported_count = sum(1 for _ in reader)
                                self.validation_results['total_records_exported'] += exported_count
                        except Exception as e:
                            self.logger.error(f"Error contando registros exportados en {table}: {e}")
                            continue
                    else:
                        exported_count = 0
                    
                    # Comparar conteos
                    if original_count == exported_count:
                        self.logger.info(f"✓ {table}: {original_count} registros (coincide)")
                        self.validation_results['data_integrity_passed'] += 1
                    else:
                        self.logger.error(f"✗ {table}: Original={original_count}, Exportado={exported_count}")
                        self.validation_results['data_integrity_failed'] += 1
                        all_counts_match = False
                        
        finally:
            conn.close()
            
        return all_counts_match
        
    def validate_referential_integrity(self) -> bool:
        """Validar integridad referencial básica en archivos CSV"""
        self.logger.info("Validando integridad referencial...")
        
        try:
            # Cargar datos de dependencies
            deps_file = self.export_dir / "data_dependencies.csv"
            if not deps_file.exists():
                self.logger.warning("No se puede validar integridad referencial: falta archivo dependencies")
                return True
                
            with open(deps_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                dependency_ids = {row['id'] for row in reader}
                
            # Validar subdependencies
            subdeps_file = self.export_dir / "data_subdependencies.csv"
            if subdeps_file.exists():
                with open(subdeps_file, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for i, row in enumerate(reader):
                        if row['dependency_id'] not in dependency_ids:
                            self.logger.error(f"Integridad referencial violada en subdependencies fila {i+1}: dependency_id={row['dependency_id']}")
                            return False
                            
            # Validar procedures
            procs_file = self.export_dir / "data_procedures.csv"
            if procs_file.exists():
                with open(procs_file, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for i, row in enumerate(reader):
                        if row['dependency_id'] and row['dependency_id'] not in dependency_ids:
                            self.logger.error(f"Integridad referencial violada en procedures fila {i+1}: dependency_id={row['dependency_id']}")
                            return False
                            
            # Validar opas
            opas_file = self.export_dir / "data_opas.csv"
            if opas_file.exists():
                with open(opas_file, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for i, row in enumerate(reader):
                        if row['dependency_id'] and row['dependency_id'] not in dependency_ids:
                            self.logger.error(f"Integridad referencial violada en opas fila {i+1}: dependency_id={row['dependency_id']}")
                            return False
                            
            self.logger.info("✓ Integridad referencial validada correctamente")
            return True
            
        except Exception as e:
            self.logger.error(f"Error validando integridad referencial: {e}")
            return False
            
    def generate_validation_report(self):
        """Generar reporte de validación"""
        report_file = self.export_dir / "validation_report.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# Reporte de Validación - Exportación Municipal Chía\n\n")
            f.write(f"**Fecha de validación:** {os.popen('date').read().strip()}\n")
            f.write(f"**Directorio validado:** {self.export_dir}\n\n")
            
            f.write("## Resumen de Validación\n\n")
            f.write(f"- **Archivos verificados:** {self.validation_results['files_checked']}\n")
            f.write(f"- **Archivos faltantes:** {self.validation_results['files_missing']}\n")
            f.write(f"- **Tablas con integridad correcta:** {self.validation_results['data_integrity_passed']}\n")
            f.write(f"- **Tablas con problemas de integridad:** {self.validation_results['data_integrity_failed']}\n")
            f.write(f"- **Total registros originales:** {self.validation_results['total_records_original']:,}\n")
            f.write(f"- **Total registros exportados:** {self.validation_results['total_records_exported']:,}\n\n")
            
            # Estado general
            if (self.validation_results['files_missing'] == 0 and 
                self.validation_results['data_integrity_failed'] == 0):
                f.write("## ✅ Estado: VALIDACIÓN EXITOSA\n\n")
                f.write("La exportación ha pasado todas las validaciones.\n")
            else:
                f.write("## ❌ Estado: VALIDACIÓN CON ERRORES\n\n")
                f.write("Se encontraron problemas que requieren atención.\n")
                
        self.logger.info(f"✓ Reporte de validación generado: {report_file}")
        
    def run_validation(self) -> bool:
        """Ejecutar validación completa"""
        self.logger.info("🔍 Iniciando validación de exportación...")
        
        validation_passed = True
        
        # 1. Validar directorio y archivos
        if not self.validate_export_directory():
            validation_passed = False
            
        # 2. Validar archivos CSV
        for table in self.main_tables:
            if not self.validate_csv_integrity(table):
                validation_passed = False
                
        # 3. Validar archivos SQL
        for table in self.main_tables:
            if not self.validate_sql_syntax(table):
                validation_passed = False
                
        # 4. Comparar conteos de registros
        if not self.compare_record_counts():
            validation_passed = False
            
        # 5. Validar integridad referencial
        if not self.validate_referential_integrity():
            validation_passed = False
            
        # 6. Generar reporte
        self.generate_validation_report()
        
        if validation_passed:
            self.logger.info("🎉 Validación completada exitosamente!")
            self.logger.info("✅ La exportación es íntegra y está lista para usar")
        else:
            self.logger.error("❌ Validación falló - revisar errores arriba")
            
        return validation_passed

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Uso: python validate_export.py <directorio_exportacion>")
        print("Ejemplo: python validate_export.py database_exports/export_20250106_143022")
        sys.exit(1)
        
    export_directory = sys.argv[1]
    validator = ExportValidator(export_directory)
    
    success = validator.run_validation()
    sys.exit(0 if success else 1)
