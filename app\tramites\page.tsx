import { createClient } from '@/lib/supabase/server'
import { getCurrentUser, getUserProfile } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { ProtectedLayout } from '@/components/layout'
import { CitizenRouteGuard } from '@/components/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ContextualFAQSection } from '@/components/faq/ContextualFAQSection'
import {
  FileText,
  Plus,
  Search,
  Filter,
  Clock,
  CheckCircle,
  AlertCircle,
  Eye
} from 'lucide-react'

export default async function TramitesPage() {
  const { user, error } = await getCurrentUser()
  
  if (error || !user) {
    redirect('/auth/login')
  }

  const { data: profile } = await getUserProfile(user.id)
  
  if (!profile) {
    redirect('/auth/setup-profile')
  }

  // Get user's procedures
  const supabase = createClient()
  const { data: procedures } = await supabase
    .from('citizen_procedures')
    .select(`
      *,
      procedure:procedures(name, description, cost, estimated_days),
      status:procedure_statuses(name, display_name, color),
      dependency:procedures(dependency:dependencies(name))
    `)
    .eq('citizen_id', user.id)
    .order('created_at', { ascending: false })

  // Get available procedures for new applications
  const { data: availableProcedures } = await supabase
    .from('procedures')
    .select(`
      *,
      dependency:dependencies(name)
    `)
    .eq('is_active', true)
    .order('name')
    .limit(6)

  return (
    <ProtectedLayout requireAuth={true} allowedRoles={['ciudadano']}>
      <CitizenRouteGuard>
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Mis Trámites
                </h1>
                <p className="text-gray-600">
                  Gestiona y consulta el estado de tus trámites
                </p>
              </div>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Nuevo Trámite
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Trámites</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{procedures?.length || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Trámites iniciados
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">En Proceso</CardTitle>
                <Clock className="h-4 w-4 text-yellow-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">
                  {procedures?.filter(p => p.status?.name === 'pending').length || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Pendientes
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Completados</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {procedures?.filter(p => p.status?.name === 'completed').length || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Finalizados
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* My Procedures */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>Mis Trámites</CardTitle>
                      <CardDescription>
                        Lista de todos tus trámites y su estado actual
                      </CardDescription>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        <Search className="h-4 w-4 mr-2" />
                        Buscar
                      </Button>
                      <Button variant="outline" size="sm">
                        <Filter className="h-4 w-4 mr-2" />
                        Filtrar
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {procedures && procedures.length > 0 ? (
                    <div className="space-y-4">
                      {procedures.map((procedure: any) => (
                        <div key={procedure.id} className="border rounded-lg p-4">
                          <div className="flex justify-between items-start mb-3">
                            <div className="flex-1">
                              <h3 className="font-medium text-gray-900">
                                {procedure.procedure?.name || 'Trámite'}
                              </h3>
                              <p className="text-sm text-gray-500 mt-1">
                                Ref: {procedure.reference_number}
                              </p>
                              <p className="text-sm text-gray-500">
                                Dependencia: {procedure.dependency?.dependency?.name || 'N/A'}
                              </p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge 
                                variant="outline"
                                className="text-xs"
                              >
                                {procedure.status?.display_name || 'Pendiente'}
                              </Badge>
                              <Button variant="outline" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <p className="text-gray-500">Fecha de Inicio</p>
                              <p className="font-medium">
                                {new Date(procedure.created_at).toLocaleDateString('es-CO')}
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-500">Costo</p>
                              <p className="font-medium">
                                {procedure.procedure?.cost ? 
                                  `$${procedure.procedure.cost.toLocaleString('es-CO')}` : 
                                  'Gratuito'
                                }
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-500">Tiempo Estimado</p>
                              <p className="font-medium">
                                {procedure.procedure?.estimated_days || 'N/A'} días
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-500">Estado</p>
                              <p className="font-medium">
                                {procedure.status?.display_name || 'Pendiente'}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <FileText className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        No tienes trámites iniciados
                      </h3>
                      <p className="text-gray-500 mb-4">
                        Comienza tu primer trámite para ver el progreso aquí
                      </p>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Iniciar Primer Trámite
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Available Procedures */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Trámites Disponibles</CardTitle>
                  <CardDescription>
                    Inicia un nuevo trámite
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {availableProcedures && availableProcedures.length > 0 ? (
                    <div className="space-y-3">
                      {availableProcedures.map((procedure: any) => (
                        <div key={procedure.id} className="border rounded-lg p-3 hover:bg-gray-50 cursor-pointer">
                          <h4 className="font-medium text-sm text-gray-900 mb-1">
                            {procedure.name}
                          </h4>
                          <p className="text-xs text-gray-500 mb-2">
                            {procedure.dependency?.name}
                          </p>
                          <div className="flex justify-between items-center">
                            <span className="text-xs text-gray-500">
                              {procedure.cost ? 
                                `$${procedure.cost.toLocaleString('es-CO')}` : 
                                'Gratuito'
                              }
                            </span>
                            <Button size="sm" variant="outline">
                              Iniciar
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 text-center py-4">
                      No hay trámites disponibles
                    </p>
                  )}
                  
                  <Button className="w-full mt-4" variant="outline">
                    Ver Todos los Trámites
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* FAQ Section - Contextual for citizen procedures */}
          <div className="bg-white border-t mt-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
              <ContextualFAQSection context="citizen" />
            </div>
          </div>
        </div>
      </CitizenRouteGuard>
    </ProtectedLayout>
  )
}
