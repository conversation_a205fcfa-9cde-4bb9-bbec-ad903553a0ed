import { createClient } from '@/lib/supabase/server'
import { getCurrentUser, getUserProfile } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { ProtectedLayout } from '@/components/layout'
import { RouteGuard } from '@/components/navigation'
import { ProcedureManagementInterface } from '@/components/procedures/ProcedureManagementInterface'
import { ContextualFAQSection } from '@/components/faq/ContextualFAQSection'

export default async function GestionTramitesPage() {
  const { user, error } = await getCurrentUser()
  
  if (error || !user) {
    redirect('/auth/login')
  }

  const { data: profile } = await getUserProfile(user.id)
  
  if (!profile) {
    redirect('/auth/setup-profile')
  }

  const supabase = createClient()

  // Get user's procedures with full details
  const { data: userProcedures } = await supabase
    .from('citizen_procedures')
    .select(`
      *,
      procedure:procedures(
        *,
        dependency:dependencies(
          id,
          name,
          acronym,
          contact_email,
          contact_phone,
          address
        )
      ),
      status:procedure_statuses(*),
      assigned_to:profiles(
        id,
        full_name,
        email
      )
    `)
    .eq('citizen_id', user.id)
    .order('created_at', { ascending: false })

  // Get all procedure statuses for status management
  const { data: procedureStatuses } = await supabase
    .from('procedure_statuses')
    .select('*')
    .order('order_index', { ascending: true })

  // Get available procedures for new procedure initiation
  const { data: availableProcedures } = await supabase
    .from('procedures')
    .select(`
      *,
      dependency:dependencies(
        id,
        name,
        acronym,
        contact_email,
        contact_phone
      )
    `)
    .eq('is_active', true)
    .order('name', { ascending: true })

  // Get user's recent notifications
  const { data: notifications } = await supabase
    .from('notifications')
    .select('*')
    .eq('user_id', user.id)
    .eq('is_read', false)
    .order('created_at', { ascending: false })
    .limit(10)

  return (
    <RouteGuard allowedRoles={['ciudadano', 'admin', 'super_admin']}>
      <ProtectedLayout>
        <div className="min-h-screen bg-gray-50">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white shadow-lg">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between items-center py-8">
                <div>
                  <h1 className="text-3xl font-bold">
                    Gestión de Trámites
                  </h1>
                  <p className="text-blue-100 mt-1">
                    Inicia, gestiona y da seguimiento a tus trámites municipales
                  </p>
                  <div className="flex items-center mt-3 space-x-4 text-sm">
                    <div className="flex items-center">
                      <span className="font-medium">{userProcedures?.length || 0}</span>
                      <span className="ml-1">trámites activos</span>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium">{notifications?.length || 0}</span>
                      <span className="ml-1">notificaciones pendientes</span>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium">{availableProcedures?.length || 0}</span>
                      <span className="ml-1">trámites disponibles</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <ProcedureManagementInterface
              userProcedures={userProcedures || []}
              availableProcedures={availableProcedures || []}
              procedureStatuses={procedureStatuses || []}
              notifications={notifications || []}
              currentUser={user}
              userProfile={profile}
            />
          </div>

          {/* FAQ Section - Contextual for procedure management */}
          <div className="bg-gray-50 border-t">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
              <ContextualFAQSection context="management" />
            </div>
          </div>
        </div>
      </ProtectedLayout>
    </RouteGuard>
  )
}
