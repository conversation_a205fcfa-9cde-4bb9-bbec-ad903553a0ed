'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { 
  Activity, 
  Zap, 
  Clock, 
  Eye, 
  Smartphone, 
  Monitor, 
  Wifi, 
  HardDrive,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  AlertTriangle
} from 'lucide-react'
import { PerformanceMonitor } from '@/lib/performance'
import { useDeviceDetection, useMobilePerformance } from '@/lib/mobile'

interface PerformanceMetrics {
  lcp: number // Largest Contentful Paint
  fid: number // First Input Delay
  cls: number // Cumulative Layout Shift
  fcp: number // First Contentful Paint
  ttfb: number // Time to First Byte
  loadTime: number
  domContentLoaded: number
  memoryUsage: number
  cacheHitRate: number
  apiResponseTime: number
}

interface DeviceStats {
  mobile: number
  tablet: number
  desktop: number
  totalUsers: number
}

export function PerformanceDashboard() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [deviceStats, setDeviceStats] = useState<DeviceStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())
  
  const { isMobile, isTablet, isDesktop, screenSize } = useDeviceDetection()
  const { isLowEndDevice } = useMobilePerformance()

  // Fetch performance metrics
  const fetchMetrics = async () => {
    setIsLoading(true)
    try {
      // Simulate API call - replace with actual endpoint
      const response = await fetch('/api/admin/performance-metrics')
      if (response.ok) {
        const data = await response.json()
        setMetrics(data.metrics)
        setDeviceStats(data.deviceStats)
      } else {
        // Fallback to client-side metrics
        const clientMetrics = await collectClientMetrics()
        setMetrics(clientMetrics)
      }
    } catch (error) {
      console.error('Failed to fetch performance metrics:', error)
      // Use client-side metrics as fallback
      const clientMetrics = await collectClientMetrics()
      setMetrics(clientMetrics)
    } finally {
      setIsLoading(false)
      setLastUpdated(new Date())
    }
  }

  // Collect client-side performance metrics
  const collectClientMetrics = async (): Promise<PerformanceMetrics> => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    const paint = performance.getEntriesByType('paint')
    
    const fcp = paint.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0
    const lcp = await getLCP()
    const cls = await getCLS()
    const fid = await getFID()

    return {
      lcp,
      fid,
      cls,
      fcp,
      ttfb: navigation.responseStart - navigation.requestStart,
      loadTime: navigation.loadEventEnd - navigation.navigationStart,
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
      memoryUsage: (performance as any).memory?.usedJSHeapSize || 0,
      cacheHitRate: 85, // Placeholder
      apiResponseTime: 150 // Placeholder
    }
  }

  // Get Largest Contentful Paint
  const getLCP = (): Promise<number> => {
    return new Promise((resolve) => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          resolve(lastEntry.startTime)
          observer.disconnect()
        })
        observer.observe({ entryTypes: ['largest-contentful-paint'] })
        
        // Timeout after 5 seconds
        setTimeout(() => {
          observer.disconnect()
          resolve(0)
        }, 5000)
      } else {
        resolve(0)
      }
    })
  }

  // Get Cumulative Layout Shift
  const getCLS = (): Promise<number> => {
    return new Promise((resolve) => {
      let clsValue = 0
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value
            }
          }
        })
        observer.observe({ entryTypes: ['layout-shift'] })
        
        setTimeout(() => {
          observer.disconnect()
          resolve(clsValue)
        }, 5000)
      } else {
        resolve(0)
      }
    })
  }

  // Get First Input Delay
  const getFID = (): Promise<number> => {
    return new Promise((resolve) => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            resolve((entry as any).processingStart - entry.startTime)
            observer.disconnect()
            return
          }
        })
        observer.observe({ entryTypes: ['first-input'] })
        
        setTimeout(() => {
          observer.disconnect()
          resolve(0)
        }, 10000)
      } else {
        resolve(0)
      }
    })
  }

  // Get performance score based on Core Web Vitals
  const getPerformanceScore = (metrics: PerformanceMetrics): number => {
    let score = 100
    
    // LCP scoring (good: <2.5s, needs improvement: 2.5-4s, poor: >4s)
    if (metrics.lcp > 4000) score -= 30
    else if (metrics.lcp > 2500) score -= 15
    
    // FID scoring (good: <100ms, needs improvement: 100-300ms, poor: >300ms)
    if (metrics.fid > 300) score -= 25
    else if (metrics.fid > 100) score -= 10
    
    // CLS scoring (good: <0.1, needs improvement: 0.1-0.25, poor: >0.25)
    if (metrics.cls > 0.25) score -= 25
    else if (metrics.cls > 0.1) score -= 10
    
    // Additional factors
    if (metrics.ttfb > 600) score -= 10
    if (metrics.loadTime > 3000) score -= 10
    
    return Math.max(0, score)
  }

  const getScoreColor = (score: number): string => {
    if (score >= 90) return 'text-green-600'
    if (score >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreBadgeVariant = (score: number): 'default' | 'secondary' | 'destructive' => {
    if (score >= 90) return 'default'
    if (score >= 70) return 'secondary'
    return 'destructive'
  }

  useEffect(() => {
    fetchMetrics()
    
    // Auto-refresh every 5 minutes
    const interval = setInterval(fetchMetrics, 5 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])

  if (isLoading || !metrics) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Panel de Rendimiento</h2>
          <div className="animate-spin">
            <RefreshCw className="h-5 w-5" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  const performanceScore = getPerformanceScore(metrics)

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Panel de Rendimiento</h2>
          <p className="text-sm text-gray-600">
            Última actualización: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={getScoreBadgeVariant(performanceScore)}>
            Puntuación: {performanceScore}/100
          </Badge>
          <Button onClick={fetchMetrics} size="sm" variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualizar
          </Button>
        </div>
      </div>

      <Tabs defaultValue="vitals" className="space-y-4">
        <TabsList>
          <TabsTrigger value="vitals">Core Web Vitals</TabsTrigger>
          <TabsTrigger value="performance">Rendimiento</TabsTrigger>
          <TabsTrigger value="devices">Dispositivos</TabsTrigger>
          <TabsTrigger value="optimization">Optimización</TabsTrigger>
        </TabsList>

        <TabsContent value="vitals" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Eye className="h-4 w-4 mr-2" />
                  LCP (Largest Contentful Paint)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {(metrics.lcp / 1000).toFixed(2)}s
                </div>
                <Progress 
                  value={Math.min(100, (2500 / metrics.lcp) * 100)} 
                  className="mt-2"
                />
                <p className="text-xs text-gray-600 mt-1">
                  {metrics.lcp <= 2500 ? 'Bueno' : metrics.lcp <= 4000 ? 'Mejorable' : 'Pobre'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Zap className="h-4 w-4 mr-2" />
                  FID (First Input Delay)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {metrics.fid.toFixed(0)}ms
                </div>
                <Progress 
                  value={Math.min(100, (100 / metrics.fid) * 100)} 
                  className="mt-2"
                />
                <p className="text-xs text-gray-600 mt-1">
                  {metrics.fid <= 100 ? 'Bueno' : metrics.fid <= 300 ? 'Mejorable' : 'Pobre'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Activity className="h-4 w-4 mr-2" />
                  CLS (Cumulative Layout Shift)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {metrics.cls.toFixed(3)}
                </div>
                <Progress 
                  value={Math.min(100, (0.1 / metrics.cls) * 100)} 
                  className="mt-2"
                />
                <p className="text-xs text-gray-600 mt-1">
                  {metrics.cls <= 0.1 ? 'Bueno' : metrics.cls <= 0.25 ? 'Mejorable' : 'Pobre'}
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Clock className="h-4 w-4 mr-2" />
                  Tiempo de Carga
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {(metrics.loadTime / 1000).toFixed(2)}s
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Wifi className="h-4 w-4 mr-2" />
                  TTFB
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {metrics.ttfb.toFixed(0)}ms
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <HardDrive className="h-4 w-4 mr-2" />
                  Uso de Memoria
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {(metrics.memoryUsage / 1024 / 1024).toFixed(1)}MB
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Cache Hit Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {metrics.cacheHitRate}%
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="devices" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Smartphone className="h-4 w-4 mr-2" />
                  Móvil
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {deviceStats?.mobile || 0}%
                </div>
                <p className="text-xs text-gray-600">
                  {isMobile ? 'Dispositivo actual' : ''}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Monitor className="h-4 w-4 mr-2" />
                  Escritorio
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {deviceStats?.desktop || 0}%
                </div>
                <p className="text-xs text-gray-600">
                  {isDesktop ? 'Dispositivo actual' : ''}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Monitor className="h-4 w-4 mr-2" />
                  Tablet
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {deviceStats?.tablet || 0}%
                </div>
                <p className="text-xs text-gray-600">
                  {isTablet ? 'Dispositivo actual' : ''}
                </p>
              </CardContent>
            </Card>
          </div>

          {isLowEndDevice && (
            <Card className="border-yellow-200 bg-yellow-50">
              <CardHeader>
                <CardTitle className="text-sm font-medium flex items-center text-yellow-800">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Dispositivo de Gama Baja Detectado
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-yellow-700">
                  Se han aplicado optimizaciones automáticas para mejorar el rendimiento en este dispositivo.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="optimization" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recomendaciones de Optimización</CardTitle>
                <CardDescription>
                  Sugerencias basadas en las métricas actuales
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {metrics.lcp > 2500 && (
                  <div className="flex items-start space-x-2">
                    <TrendingDown className="h-4 w-4 text-red-500 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium">Optimizar LCP</p>
                      <p className="text-xs text-gray-600">
                        Considera lazy loading de imágenes y optimización de recursos críticos
                      </p>
                    </div>
                  </div>
                )}
                
                {metrics.fid > 100 && (
                  <div className="flex items-start space-x-2">
                    <TrendingDown className="h-4 w-4 text-red-500 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium">Reducir FID</p>
                      <p className="text-xs text-gray-600">
                        Optimiza JavaScript y reduce el tiempo de ejecución
                      </p>
                    </div>
                  </div>
                )}
                
                {metrics.cls > 0.1 && (
                  <div className="flex items-start space-x-2">
                    <TrendingDown className="h-4 w-4 text-red-500 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium">Mejorar CLS</p>
                      <p className="text-xs text-gray-600">
                        Define dimensiones para imágenes y evita contenido dinámico
                      </p>
                    </div>
                  </div>
                )}
                
                {performanceScore >= 90 && (
                  <div className="flex items-start space-x-2">
                    <TrendingUp className="h-4 w-4 text-green-500 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium">Excelente rendimiento</p>
                      <p className="text-xs text-gray-600">
                        Tu sitio está optimizado correctamente
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Estado del Sistema</CardTitle>
                <CardDescription>
                  Información técnica del dispositivo actual
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm">Tamaño de pantalla:</span>
                  <Badge variant="outline">{screenSize}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Tipo de dispositivo:</span>
                  <Badge variant="outline">
                    {isMobile ? 'Móvil' : isTablet ? 'Tablet' : 'Escritorio'}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Dispositivo de gama baja:</span>
                  <Badge variant={isLowEndDevice ? 'destructive' : 'default'}>
                    {isLowEndDevice ? 'Sí' : 'No'}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
