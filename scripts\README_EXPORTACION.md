# 📊 Sistema de Exportación - Base de Datos Municipal Chía

Este directorio contiene scripts especializados para exportar las tablas principales del sistema municipal de Chía, incluyendo estructura, datos y políticas RLS.

## 🎯 Tablas Exportadas

Las siguientes tablas principales son exportadas por todos los scripts:

1. **`procedures`** - Trámites municipales (108 registros)
2. **`opas`** - Otras Prestaciones de Atención (721 registros)
3. **`dependencies`** - Dependencias municipales (12 registros)
4. **`subdependencies`** - Subdependencias organizacionales (75 registros)
5. **`municipal_faqs`** - Preguntas frecuentes del sistema
6. **`faq_themes`** - Temas de preguntas frecuentes

## 🛠️ Scripts Disponibles

### 1. Script Python (Recomendado) 🐍
**Archivo:** `export_municipal_database.py`

**Ventajas:**
- Multiplataforma (Windows, Linux, macOS)
- Manejo robusto de errores
- Logging detallado
- Exportación en múltiples formatos

**Requisitos:**
```bash
pip install psycopg2-binary python-dotenv
```

**Uso:**
```bash
# Configurar variables de entorno
export SUPABASE_DB_HOST="db.zeieudvbhlrlnfkwejoh.supabase.co"
export SUPABASE_DB_PORT="5432"
export SUPABASE_DB_NAME="postgres"
export SUPABASE_DB_USER="postgres"
export SUPABASE_DB_PASSWORD="tu_password_aqui"

# Ejecutar exportación
python export_municipal_database.py
```

### 2. Script PowerShell (Windows) 💻
**Archivo:** `Export-MunicipalDB.ps1`

**Ventajas:**
- Nativo para Windows
- Interfaz colorizada
- Parámetros flexibles

**Uso:**
```powershell
# Configurar variables de entorno
$env:SUPABASE_DB_PASSWORD = "tu_password_aqui"

# Ejecutar exportación
.\Export-MunicipalDB.ps1
```

### 3. Script Bash (Linux/macOS) 🐧
**Archivo:** `export_municipal_db.sh`

**Ventajas:**
- Nativo para sistemas Unix
- Uso de herramientas estándar PostgreSQL
- Logging colorizado

**Uso:**
```bash
# Hacer ejecutable
chmod +x export_municipal_db.sh

# Configurar variables de entorno
export SUPABASE_DB_PASSWORD="tu_password_aqui"

# Ejecutar exportación
./export_municipal_db.sh
```

### 4. Script SQL Directo 📝
**Archivo:** `export_database.sql`

**Ventajas:**
- Ejecución directa en psql
- Control granular
- Personalizable

**Uso:**
```bash
psql -h db.zeieudvbhlrlnfkwejoh.supabase.co -p 5432 -U postgres -d postgres -f export_database.sql
```

## 🔧 Configuración de Variables de Entorno

### Opción 1: Variables del Sistema
```bash
# Linux/macOS
export SUPABASE_DB_HOST="db.zeieudvbhlrlnfkwejoh.supabase.co"
export SUPABASE_DB_PORT="5432"
export SUPABASE_DB_NAME="postgres"
export SUPABASE_DB_USER="postgres"
export SUPABASE_DB_PASSWORD="tu_password_aqui"

# Windows PowerShell
$env:SUPABASE_DB_HOST = "db.zeieudvbhlrlnfkwejoh.supabase.co"
$env:SUPABASE_DB_PORT = "5432"
$env:SUPABASE_DB_NAME = "postgres"
$env:SUPABASE_DB_USER = "postgres"
$env:SUPABASE_DB_PASSWORD = "tu_password_aqui"
```

### Opción 2: Archivo .env
Crear archivo `.env` en el directorio scripts:
```env
SUPABASE_DB_HOST=db.zeieudvbhlrlnfkwejoh.supabase.co
SUPABASE_DB_PORT=5432
SUPABASE_DB_NAME=postgres
SUPABASE_DB_USER=postgres
SUPABASE_DB_PASSWORD=tu_password_aqui
```

## 📁 Estructura de Archivos Generados

Cada exportación crea un directorio con timestamp:
```
database_exports/
└── export_20250106_143022/
    ├── 00_export_summary.md          # Resumen de la exportación
    ├── 01_complete_structure.sql     # Estructura completa
    ├── structure_dependencies.sql    # Estructura por tabla
    ├── structure_subdependencies.sql
    ├── structure_procedures.sql
    ├── structure_opas.sql
    ├── structure_faq_themes.sql
    ├── structure_municipal_faqs.sql
    ├── data_dependencies.sql         # Datos en formato SQL
    ├── data_subdependencies.sql
    ├── data_procedures.sql
    ├── data_opas.sql
    ├── data_faq_themes.sql
    ├── data_municipal_faqs.sql
    ├── data_dependencies.csv         # Datos en formato CSV
    ├── data_subdependencies.csv
    ├── data_procedures.csv
    ├── data_opas.csv
    ├── data_faq_themes.csv
    ├── data_municipal_faqs.csv
    ├── 03_rls_policies.sql          # Políticas RLS
    ├── 04_functions_triggers.sql    # Funciones y triggers
    └── restore_database.sh          # Script de restauración
```

## 🔄 Restauración de Base de Datos

### Usando el Script de Restauración Automático
```bash
cd database_exports/export_20250106_143022/
./restore_database.sh localhost 5432 nueva_db postgres
```

### Restauración Manual
```bash
# 1. Crear nueva base de datos
createdb nueva_db_chia

# 2. Restaurar estructura
psql -d nueva_db_chia -f 01_complete_structure.sql

# 3. Restaurar datos (en orden de dependencias)
psql -d nueva_db_chia -f data_dependencies.sql
psql -d nueva_db_chia -f data_subdependencies.sql
psql -d nueva_db_chia -f data_faq_themes.sql
psql -d nueva_db_chia -f data_procedures.sql
psql -d nueva_db_chia -f data_opas.sql
psql -d nueva_db_chia -f data_municipal_faqs.sql

# 4. Aplicar políticas RLS
psql -d nueva_db_chia -f 03_rls_policies.sql

# 5. Restaurar funciones y triggers
psql -d nueva_db_chia -f 04_functions_triggers.sql
```

## 🚨 Consideraciones de Seguridad

### Datos Sensibles
- Los archivos exportados pueden contener información sensible
- Almacenar en ubicaciones seguras
- No subir a repositorios públicos
- Cifrar archivos para transporte

### Políticas RLS
- Las políticas RLS se exportan y deben restaurarse
- Verificar que las funciones auxiliares estén disponibles
- Probar acceso después de la restauración

### Credenciales
- Nunca hardcodear passwords en scripts
- Usar variables de entorno o archivos .env
- Rotar credenciales regularmente

## 📋 Checklist de Exportación

- [ ] Variables de entorno configuradas
- [ ] PostgreSQL client tools instalados
- [ ] Conexión a base de datos verificada
- [ ] Espacio en disco suficiente
- [ ] Permisos de escritura en directorio destino
- [ ] Backup de seguridad antes de exportar

## 🔍 Troubleshooting

### Error: "pg_dump: command not found"
```bash
# Ubuntu/Debian
sudo apt-get install postgresql-client

# CentOS/RHEL
sudo yum install postgresql

# macOS
brew install postgresql

# Windows
# Descargar desde https://www.postgresql.org/download/windows/
```

### Error: "psycopg2 not found"
```bash
pip install psycopg2-binary
# o
conda install psycopg2
```

### Error: "Connection refused"
- Verificar host y puerto
- Verificar firewall
- Verificar credenciales
- Verificar que Supabase esté activo

### Error: "Permission denied"
- Verificar permisos de usuario en base de datos
- Verificar políticas RLS
- Usar usuario con permisos administrativos

## 📞 Soporte

Para problemas con la exportación:
1. Revisar logs generados
2. Verificar configuración de variables de entorno
3. Probar conexión manual con psql
4. Consultar documentación de Supabase

## 📈 Estadísticas del Sistema

- **Total de procedimientos**: 829 (108 trámites + 721 OPAs)
- **Dependencias activas**: 12
- **Subdependencias**: 75
- **Base de datos**: PostgreSQL en Supabase
- **Proyecto ID**: zeieudvbhlrlnfkwejoh
