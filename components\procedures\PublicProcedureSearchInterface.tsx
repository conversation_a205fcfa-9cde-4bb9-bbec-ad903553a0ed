'use client'

import React, { useState, useMemo } from 'react'
import { Search, Grid, List, Eye, Clock, DollarSign, Building2, FileText, BarChart3, Mic, History } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { PublicProcedureDetailModal } from './PublicProcedureDetailModal'
import { SmartFilters, FilterState } from '@/components/search/SmartFilters'
import { ProcedureCardEnhanced } from './ProcedureCardEnhanced'
import { useIntelligentSearch } from '@/hooks/useIntelligentSearch'
import { cn } from '@/lib/utils'
import { SuitEnhancedProcedure, enhanceProcedureWithSuit } from '@/types/suit-enhanced-procedure'

interface Dependency {
  id: string
  name: string
  acronym?: string
  description?: string
  contact_email?: string
  contact_phone?: string
  address?: string
}

interface Subdependency {
  id: string
  name: string
}

interface PublicProcedureSearchInterfaceProps {
  procedures: SuitEnhancedProcedure[]
  dependencies: Dependency[]
  subdependencies: Subdependency[]
  categories: string[]
}

export function PublicProcedureSearchInterface({
  procedures,
  dependencies,
  subdependencies,
  categories
}: PublicProcedureSearchInterfaceProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [selectedProcedure, setSelectedProcedure] = useState<SuitEnhancedProcedure | null>(null)
  const [favorites, setFavorites] = useState<Set<string>>(new Set())
  const [showSuggestions, setShowSuggestions] = useState(false)

  // Hook de búsqueda inteligente
  const {
    searchTerm,
    filters,
    searchResults,
    isSearching,
    searchHistory,
    searchStats,
    updateSearchTerm,
    updateFilters,
    clearSearch,
    getSuggestions
  } = useIntelligentSearch({
    procedures,
    dependencies,
    subdependencies,
    options: {
      debounceMs: 300,
      minSearchLength: 0,
      maxResults: 50
    }
  })

  // Manejar favoritos
  const toggleFavorite = (procedureId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev)
      if (newFavorites.has(procedureId)) {
        newFavorites.delete(procedureId)
      } else {
        newFavorites.add(procedureId)
      }
      return newFavorites
    })
  }

  // Manejar sugerencias
  const handleSearchFocus = () => {
    setShowSuggestions(true)
  }

  const handleSearchBlur = () => {
    // Delay para permitir clicks en sugerencias
    setTimeout(() => setShowSuggestions(false), 200)
  }

  const handleSuggestionClick = (suggestion: string) => {
    updateSearchTerm(suggestion)
    setShowSuggestions(false)
  }

  const suggestions = getSuggestions(searchTerm)

  return (
    <div className="space-y-6">
      {/* Search Header */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
            <div>
              <CardTitle className="flex items-center">
                <Search className="mr-2 h-5 w-5 text-primary" />
                Búsqueda Inteligente de Trámites y OPAs
              </CardTitle>
              <CardDescription>
                Sistema mejorado para encontrar procedimientos municipales de forma rápida y eficiente
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              >
                {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid className="h-4 w-4" />}
              </Button>
              {searchStats.hasActiveFilters && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearSearch}
                  className="text-red-600 hover:text-red-700"
                >
                  Limpiar todo
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Enhanced Search Input */}
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              placeholder="¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia..."
              value={searchTerm}
              onChange={(e) => updateSearchTerm(e.target.value)}
              onFocus={handleSearchFocus}
              onBlur={handleSearchBlur}
              className={cn(
                "pl-12 pr-12 py-4 text-lg h-16",
                "border-2 border-gray-300/60 focus:border-primary",
                "rounded-2xl shadow-lg focus:shadow-xl transition-all duration-300",
                "bg-white/90 backdrop-blur-sm hover:bg-white focus:bg-white",
                "placeholder:text-gray-500 font-medium"
              )}
            />
            {isSearching && (
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary"></div>
              </div>
            )}

            {/* Search Suggestions */}
            {showSuggestions && (suggestions.length > 0 || searchHistory.length > 0) && (
              <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg z-[100] max-h-80 overflow-y-auto">
                {suggestions.length > 0 && (
                  <div className="p-3">
                    <div className="text-xs font-medium text-gray-500 mb-2">Sugerencias</div>
                    {suggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        type="button"
                        className="w-full text-left px-3 py-2 hover:bg-gray-50 rounded-lg text-sm"
                        onClick={() => handleSuggestionClick(suggestion)}
                      >
                        <Search className="inline h-4 w-4 mr-2 text-gray-400" />
                        {suggestion}
                      </button>
                    ))}
                  </div>
                )}
                {searchHistory.length > 0 && (
                  <div className="p-3 border-t border-gray-100">
                    <div className="text-xs font-medium text-gray-500 mb-2 flex items-center">
                      <History className="h-3 w-3 mr-1" />
                      Búsquedas recientes
                    </div>
                    {searchHistory.slice(0, 5).map((historyItem, index) => (
                      <button
                        key={index}
                        type="button"
                        className="w-full text-left px-3 py-2 hover:bg-gray-50 rounded-lg text-sm text-gray-600"
                        onClick={() => handleSuggestionClick(historyItem)}
                      >
                        <History className="inline h-4 w-4 mr-2 text-gray-400" />
                        {historyItem}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>

        </CardContent>
      </Card>

      {/* Smart Filters */}
      <div className="mt-8">
        <SmartFilters
          dependencies={dependencies}
          subdependencies={subdependencies}
          onFiltersChange={updateFilters}
          initialFilters={filters}
          className="mb-6"
        />
      </div>

      {/* Search Stats */}
      <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
        <div className="flex items-center space-x-4">
          <span>
            Mostrando <span className="font-medium text-gray-900">{searchResults.length}</span> de{' '}
            <span className="font-medium text-gray-900">{procedures.length}</span> procedimientos
            {(searchStats.hasActiveFilters || searchStats.hasSearchTerm) && (
              <span className="text-primary ml-1">(filtrados)</span>
            )}
          </span>
          {searchStats.hasSearchTerm && (
            <Badge variant="outline" className="text-primary border-primary/30">
              Búsqueda: "{searchTerm}"
            </Badge>
          )}
          {searchStats.hasActiveFilters && !searchStats.hasSearchTerm && (
            <Badge variant="outline" className="text-chia-green-700 border-chia-green-300">
              Con filtros activos
            </Badge>
          )}
        </div>
        <div className="text-xs text-gray-500">
          {isSearching ? 'Buscando...' : 'Búsqueda completada'}
        </div>
      </div>

      {/* Procedures Results */}
      {searchResults.length > 0 ? (
        <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
          {searchResults.map((procedure) => (
            <ProcedureCardEnhanced
              key={procedure.id}
              procedure={procedure}
              onViewDetails={setSelectedProcedure}
              onToggleFavorite={toggleFavorite}
              isFavorite={favorites.has(procedure.id)}
              layout={viewMode === 'grid' ? 'compact' : 'detailed'}
              showPreview={true}
            />
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <Search className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchStats.hasSearchTerm || searchStats.hasActiveFilters
                ? 'No se encontraron procedimientos'
                : 'Comienza tu búsqueda'
              }
            </h3>
            <p className="text-gray-500 mb-4">
              {searchStats.hasSearchTerm || searchStats.hasActiveFilters
                ? 'Intenta ajustar tus criterios de búsqueda o filtros'
                : 'Usa la barra de búsqueda o los filtros para encontrar trámites y OPAs'
              }
            </p>
            {(searchStats.hasSearchTerm || searchStats.hasActiveFilters) && (
              <Button variant="outline" onClick={clearSearch}>
                Limpiar búsqueda
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Procedure Detail Modal */}
      {selectedProcedure && (
        <PublicProcedureDetailModal
          procedure={selectedProcedure}
          onClose={() => setSelectedProcedure(null)}
        />
      )}
    </div>
  )
}
