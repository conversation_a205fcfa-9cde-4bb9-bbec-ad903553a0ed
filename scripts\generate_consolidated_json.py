#!/usr/bin/env python3
"""
=============================================
GENERADOR DE JSON CONSOLIDADO - SISTEMA MUNICIPAL CHÍA
=============================================
Genera un archivo JSON consolidado con toda la información estructurada jerárquicamente
Fecha: 2025-01-06
Autor: Sistema Municipal Chía - JSON Generator
=============================================
"""

import os
import sys
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

try:
    import psycopg2
    from psycopg2.extras import RealDictCursor
except ImportError:
    print("Error: psycopg2 no está instalado. Instala con: pip install psycopg2-binary")
    sys.exit(1)

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("Advertencia: python-dotenv no está instalado. Usando variables de entorno del sistema.")

class MunicipalJSONGenerator:
    """Generador de JSON consolidado para el sistema municipal de Chía"""
    
    def __init__(self):
        self.setup_logging()
        self.setup_config()
        
    def setup_logging(self):
        """Configurar logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_config(self):
        """Configurar conexión a la base de datos"""
        self.db_config = {
            'host': os.getenv('SUPABASE_DB_HOST', 'db.zeieudvbhlrlnfkwejoh.supabase.co'),
            'port': int(os.getenv('SUPABASE_DB_PORT', '5432')),
            'database': os.getenv('SUPABASE_DB_NAME', 'postgres'),
            'user': os.getenv('SUPABASE_DB_USER', 'postgres'),
            'password': os.getenv('SUPABASE_DB_PASSWORD')
        }
        
        if not self.db_config['password']:
            self.logger.error("SUPABASE_DB_PASSWORD no está configurada")
            print("Por favor configura las variables de entorno de Supabase:")
            print("export SUPABASE_DB_PASSWORD='tu_password'")
            sys.exit(1)
            
    def get_connection(self):
        """Obtener conexión a la base de datos"""
        try:
            conn = psycopg2.connect(**self.db_config)
            return conn
        except psycopg2.Error as e:
            self.logger.error(f"Error conectando a la base de datos: {e}")
            sys.exit(1)
            
    def fetch_dependencies(self) -> List[Dict[str, Any]]:
        """Obtener todas las dependencias activas"""
        self.logger.info("Obteniendo dependencias...")
        
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT 
                        id, code, name, acronym, description, 
                        contact_email, contact_phone, address,
                        is_active, created_at, updated_at
                    FROM dependencies 
                    WHERE is_active = true 
                    ORDER BY name
                """)
                
                dependencies = []
                for row in cur.fetchall():
                    dep = dict(row)
                    # Convertir UUID a string y fechas a ISO format
                    dep['id'] = str(dep['id'])
                    if dep['created_at']:
                        dep['created_at'] = dep['created_at'].isoformat()
                    if dep['updated_at']:
                        dep['updated_at'] = dep['updated_at'].isoformat()
                    dependencies.append(dep)
                
                self.logger.info(f"✓ {len(dependencies)} dependencias obtenidas")
                return dependencies
                
    def fetch_subdependencies(self, dependency_id: str) -> List[Dict[str, Any]]:
        """Obtener subdependencias de una dependencia específica"""
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT 
                        id, dependency_id, code, name, acronym, description,
                        contact_email, contact_phone, is_active,
                        created_at, updated_at
                    FROM subdependencies 
                    WHERE dependency_id = %s AND is_active = true 
                    ORDER BY name
                """, (dependency_id,))
                
                subdependencies = []
                for row in cur.fetchall():
                    subdep = dict(row)
                    # Convertir UUID a string y fechas a ISO format
                    subdep['id'] = str(subdep['id'])
                    subdep['dependency_id'] = str(subdep['dependency_id'])
                    if subdep['created_at']:
                        subdep['created_at'] = subdep['created_at'].isoformat()
                    if subdep['updated_at']:
                        subdep['updated_at'] = subdep['updated_at'].isoformat()
                    subdependencies.append(subdep)
                
                return subdependencies
                
    def fetch_procedures(self, subdependency_id: str) -> List[Dict[str, Any]]:
        """Obtener trámites de una subdependencia específica"""
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT 
                        id, name, description, response_time, has_cost, cost_description,
                        requirements, documents_required, suit_url, gov_url, form_required,
                        status, metadata, created_at, updated_at
                    FROM procedures 
                    WHERE subdependency_id = %s AND status = 'active'
                    ORDER BY name
                """, (subdependency_id,))
                
                procedures = []
                for row in cur.fetchall():
                    proc = dict(row)
                    # Convertir UUID a string y fechas a ISO format
                    proc['id'] = str(proc['id'])
                    if proc['created_at']:
                        proc['created_at'] = proc['created_at'].isoformat()
                    if proc['updated_at']:
                        proc['updated_at'] = proc['updated_at'].isoformat()
                    
                    # Asegurar que requirements y documents_required sean arrays
                    if not proc['requirements']:
                        proc['requirements'] = []
                    if not proc['documents_required']:
                        proc['documents_required'] = []
                        
                    procedures.append(proc)
                
                return procedures
                
    def fetch_opas(self, subdependency_id: str) -> List[Dict[str, Any]]:
        """Obtener OPAs de una subdependencia específica"""
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT 
                        id, code, name, description, has_cost, cost_description,
                        status, metadata, created_at, updated_at
                    FROM opas 
                    WHERE subdependency_id = %s AND status = 'active' AND is_active = true
                    ORDER BY name
                """, (subdependency_id,))
                
                opas = []
                for row in cur.fetchall():
                    opa = dict(row)
                    # Convertir UUID a string y fechas a ISO format
                    opa['id'] = str(opa['id'])
                    if opa['created_at']:
                        opa['created_at'] = opa['created_at'].isoformat()
                    if opa['updated_at']:
                        opa['updated_at'] = opa['updated_at'].isoformat()
                    opas.append(opa)
                
                return opas
                
    def fetch_faq_themes(self, dependency_id: str, subdependency_id: str) -> List[Dict[str, Any]]:
        """Obtener temas de FAQ de una subdependencia específica"""
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT 
                        id, name, description, icon, color, display_order,
                        is_active, created_at, updated_at
                    FROM faq_themes 
                    WHERE (dependency_id = %s OR subdependency_id = %s) AND is_active = true
                    ORDER BY display_order, name
                """, (dependency_id, subdependency_id))
                
                themes = []
                for row in cur.fetchall():
                    theme = dict(row)
                    # Convertir UUID a string y fechas a ISO format
                    theme['id'] = str(theme['id'])
                    if theme['created_at']:
                        theme['created_at'] = theme['created_at'].isoformat()
                    if theme['updated_at']:
                        theme['updated_at'] = theme['updated_at'].isoformat()
                    themes.append(theme)
                
                return themes
                
    def fetch_faqs_by_theme(self, theme_id: str) -> List[Dict[str, Any]]:
        """Obtener FAQs de un tema específico"""
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT 
                        id, question, answer, keywords, related_procedures,
                        display_order, popularity_score, view_count,
                        helpful_votes, unhelpful_votes, is_active,
                        created_at, updated_at
                    FROM municipal_faqs 
                    WHERE theme_id = %s AND is_active = true
                    ORDER BY display_order, popularity_score DESC
                """, (theme_id,))
                
                faqs = []
                for row in cur.fetchall():
                    faq = dict(row)
                    # Convertir UUID a string y fechas a ISO format
                    faq['id'] = str(faq['id'])
                    if faq['created_at']:
                        faq['created_at'] = faq['created_at'].isoformat()
                    if faq['updated_at']:
                        faq['updated_at'] = faq['updated_at'].isoformat()
                    
                    # Asegurar que keywords y related_procedures sean arrays
                    if not faq['keywords']:
                        faq['keywords'] = []
                    if not faq['related_procedures']:
                        faq['related_procedures'] = []
                        
                    faqs.append(faq)
                
                return faqs
                
    def generate_consolidated_structure(self) -> Dict[str, Any]:
        """Generar estructura consolidada completa"""
        self.logger.info("🏗️ Generando estructura consolidada...")
        
        # Obtener dependencias
        dependencies = self.fetch_dependencies()
        
        consolidated_data = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "generator": "Sistema Municipal Chía - JSON Consolidator",
                "version": "1.0.0",
                "total_dependencies": len(dependencies),
                "database_source": "Supabase PostgreSQL"
            },
            "dependencies": []
        }
        
        total_subdependencies = 0
        total_tramites = 0
        total_opas = 0
        total_faqs = 0
        
        for dep in dependencies:
            self.logger.info(f"Procesando dependencia: {dep['name']}")
            
            # Obtener subdependencias
            subdependencies = self.fetch_subdependencies(dep['id'])
            total_subdependencies += len(subdependencies)
            
            dep_structure = {
                "id": dep['id'],
                "code": dep['code'],
                "name": dep['name'],
                "acronym": dep['acronym'],
                "description": dep['description'],
                "contact_email": dep['contact_email'],
                "contact_phone": dep['contact_phone'],
                "address": dep['address'],
                "is_active": dep['is_active'],
                "created_at": dep['created_at'],
                "updated_at": dep['updated_at'],
                "subdependencies_count": len(subdependencies),
                "subdependencies": []
            }
            
            for subdep in subdependencies:
                self.logger.info(f"  Procesando subdependencia: {subdep['name']}")
                
                # Obtener contenidos de la subdependencia
                tramites = self.fetch_procedures(subdep['id'])
                opas = self.fetch_opas(subdep['id'])
                faq_themes = self.fetch_faq_themes(dep['id'], subdep['id'])
                
                total_tramites += len(tramites)
                total_opas += len(opas)
                
                # Procesar FAQs por tema
                faqs_by_theme = []
                for theme in faq_themes:
                    faqs = self.fetch_faqs_by_theme(theme['id'])
                    total_faqs += len(faqs)

                    # Agregar información del tema a cada FAQ
                    for faq in faqs:
                        faq['tema'] = {
                            'id': theme['id'],
                            'name': theme['name'],
                            'description': theme['description'],
                            'icon': theme['icon'],
                            'color': theme['color']
                        }

                    theme_with_faqs = {
                        "theme": theme,
                        "faqs": faqs,
                        "faqs_count": len(faqs)
                    }
                    faqs_by_theme.append(theme_with_faqs)
                
                subdep_structure = {
                    "id": subdep['id'],
                    "dependency_id": subdep['dependency_id'],
                    "code": subdep['code'],
                    "name": subdep['name'],
                    "acronym": subdep['acronym'],
                    "description": subdep['description'],
                    "contact_email": subdep['contact_email'],
                    "contact_phone": subdep['contact_phone'],
                    "is_active": subdep['is_active'],
                    "created_at": subdep['created_at'],
                    "updated_at": subdep['updated_at'],
                    "counters": {
                        "tramites_count": len(tramites),
                        "opas_count": len(opas),
                        "faq_themes_count": len(faq_themes),
                        "total_faqs_count": sum(theme['faqs_count'] for theme in faqs_by_theme),
                        "total_procedures": len(tramites) + len(opas)
                    },
                    "tramites": tramites,
                    "opas": opas,
                    "faq_themes": faqs_by_theme
                }
                
                dep_structure["subdependencies"].append(subdep_structure)
            
            # Calcular totales de la dependencia
            dep_structure["counters"] = {
                "total_tramites": sum(s["counters"]["tramites_count"] for s in dep_structure["subdependencies"]),
                "total_opas": sum(s["counters"]["opas_count"] for s in dep_structure["subdependencies"]),
                "total_faq_themes": sum(s["counters"]["faq_themes_count"] for s in dep_structure["subdependencies"]),
                "total_faqs": sum(s["counters"]["total_faqs_count"] for s in dep_structure["subdependencies"]),
                "total_procedures": sum(s["counters"]["total_procedures"] for s in dep_structure["subdependencies"])
            }
            
            consolidated_data["dependencies"].append(dep_structure)
        
        # Actualizar metadata con totales
        consolidated_data["metadata"].update({
            "total_subdependencies": total_subdependencies,
            "total_tramites": total_tramites,
            "total_opas": total_opas,
            "total_faqs": total_faqs,
            "total_procedures": total_tramites + total_opas
        })
        
        self.logger.info(f"✓ Estructura consolidada generada:")
        self.logger.info(f"  - {len(dependencies)} dependencias")
        self.logger.info(f"  - {total_subdependencies} subdependencias")
        self.logger.info(f"  - {total_tramites} trámites")
        self.logger.info(f"  - {total_opas} OPAs")
        self.logger.info(f"  - {total_faqs} FAQs")
        
        return consolidated_data
        
    def save_json_file(self, data: Dict[str, Any], filename: str = "sistema_municipal_chia_consolidado.json"):
        """Guardar datos en archivo JSON"""
        output_path = Path("scripts") / filename
        output_path.parent.mkdir(exist_ok=True)
        
        self.logger.info(f"💾 Guardando archivo JSON: {output_path}")
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, sort_keys=False)
        
        # Calcular tamaño del archivo
        file_size = output_path.stat().st_size
        file_size_mb = file_size / (1024 * 1024)
        
        self.logger.info(f"✓ Archivo JSON guardado exitosamente")
        self.logger.info(f"  - Tamaño: {file_size_mb:.2f} MB")
        self.logger.info(f"  - Ubicación: {output_path.absolute()}")
        
        return output_path
        
    def validate_json_structure(self, data: Dict[str, Any]) -> bool:
        """Validar estructura del JSON generado"""
        self.logger.info("🔍 Validando estructura JSON...")
        
        try:
            # Validaciones básicas
            assert "metadata" in data, "Falta sección metadata"
            assert "dependencies" in data, "Falta sección dependencies"
            assert isinstance(data["dependencies"], list), "dependencies debe ser una lista"
            
            # Validar metadata
            metadata = data["metadata"]
            required_metadata = ["generated_at", "generator", "version", "total_dependencies"]
            for field in required_metadata:
                assert field in metadata, f"Falta campo en metadata: {field}"
            
            # Validar estructura de dependencias
            for dep in data["dependencies"]:
                required_dep_fields = ["id", "code", "name", "subdependencies", "counters"]
                for field in required_dep_fields:
                    assert field in dep, f"Falta campo en dependencia: {field}"
                
                # Validar subdependencias
                for subdep in dep["subdependencies"]:
                    required_subdep_fields = ["id", "code", "name", "tramites", "opas", "faq_themes", "counters"]
                    for field in required_subdep_fields:
                        assert field in subdep, f"Falta campo en subdependencia: {field}"
                    
                    # Validar contadores
                    counters = subdep["counters"]
                    assert len(subdep["tramites"]) == counters["tramites_count"], "Contador de trámites incorrecto"
                    assert len(subdep["opas"]) == counters["opas_count"], "Contador de OPAs incorrecto"
            
            self.logger.info("✅ Estructura JSON válida")
            return True
            
        except AssertionError as e:
            self.logger.error(f"❌ Error de validación: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Error inesperado en validación: {e}")
            return False
            
    def run_generation(self):
        """Ejecutar generación completa del JSON consolidado"""
        self.logger.info("🚀 Iniciando generación de JSON consolidado...")
        
        try:
            # 1. Generar estructura consolidada
            consolidated_data = self.generate_consolidated_structure()
            
            # 2. Validar estructura
            if not self.validate_json_structure(consolidated_data):
                self.logger.error("❌ Validación falló - abortando generación")
                sys.exit(1)
            
            # 3. Guardar archivo JSON
            output_file = self.save_json_file(consolidated_data)
            
            # 4. Generar resumen
            self.generate_summary_report(consolidated_data, output_file)
            
            self.logger.info("🎉 Generación de JSON consolidado completada exitosamente!")
            
        except Exception as e:
            self.logger.error(f"❌ Error durante la generación: {e}")
            sys.exit(1)
            
    def generate_summary_report(self, data: Dict[str, Any], json_file: Path):
        """Generar reporte resumen de la generación"""
        summary_file = json_file.parent / "consolidation_summary.md"
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("# Resumen de Consolidación - Sistema Municipal Chía\n\n")
            f.write(f"**Fecha de generación:** {data['metadata']['generated_at']}\n")
            f.write(f"**Archivo JSON:** {json_file.name}\n")
            f.write(f"**Tamaño del archivo:** {json_file.stat().st_size / (1024*1024):.2f} MB\n\n")
            
            f.write("## Estadísticas Generales\n\n")
            metadata = data['metadata']
            f.write(f"- **Total dependencias:** {metadata['total_dependencies']}\n")
            f.write(f"- **Total subdependencias:** {metadata['total_subdependencies']}\n")
            f.write(f"- **Total trámites:** {metadata['total_tramites']}\n")
            f.write(f"- **Total OPAs:** {metadata['total_opas']}\n")
            f.write(f"- **Total FAQs:** {metadata['total_faqs']}\n")
            f.write(f"- **Total procedimientos:** {metadata['total_procedures']}\n\n")
            
            f.write("## Estructura del JSON\n\n")
            f.write("```json\n")
            f.write("{\n")
            f.write('  "metadata": { ... },\n')
            f.write('  "dependencies": [\n')
            f.write('    {\n')
            f.write('      "id": "uuid",\n')
            f.write('      "code": "string",\n')
            f.write('      "name": "string",\n')
            f.write('      "subdependencies": [\n')
            f.write('        {\n')
            f.write('          "tramites": [...],\n')
            f.write('          "opas": [...],\n')
            f.write('          "faq_themes": [...]\n')
            f.write('        }\n')
            f.write('      ]\n')
            f.write('    }\n')
            f.write('  ]\n')
            f.write('}\n')
            f.write("```\n\n")
            
            f.write("## Uso del Archivo\n\n")
            f.write("Este archivo JSON consolidado puede ser utilizado para:\n")
            f.write("- APIs REST del sistema municipal\n")
            f.write("- Interfaces de usuario dinámicas\n")
            f.write("- Análisis de datos y reportes\n")
            f.write("- Sincronización con otros sistemas\n")
            f.write("- Respaldo estructurado de información\n")
        
        self.logger.info(f"📋 Resumen generado: {summary_file}")

if __name__ == "__main__":
    generator = MunicipalJSONGenerator()
    generator.run_generation()
