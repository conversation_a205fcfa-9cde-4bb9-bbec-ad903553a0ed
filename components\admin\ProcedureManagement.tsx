'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { 
  Search, 
  Filter, 
  FileText, 
  Eye, 
  Edit, 
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Calendar,
  User,
  Building,
  DollarSign
} from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { useAuth, useRole, usePermissions } from '@/hooks'
import { 
  Per<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ConditionalContent
} from '@/components/auth'

interface CitizenProcedure {
  id: string
  created_at: string
  status_id: string
  citizen_id: string
  procedure_id: string
  citizen: {
    full_name: string
    document_number: string
    email: string
  }
  procedure: {
    name: string
    cost: number
    response_time_days: number
    dependency: {
      name: string
    }
  }
  status: {
    id: string
    name: string
    display_name: string
  }
}

export function ProcedureManagement() {
  const [procedures, setProcedures] = useState<CitizenProcedure[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [procedureTypeFilter, setProcedureTypeFilter] = useState('all')
  const [selectedProcedure, setSelectedProcedure] = useState<CitizenProcedure | null>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [procedureTypes, setProcedureTypes] = useState<any[]>([])
  const [statuses, setStatuses] = useState<any[]>([])

  const { profile } = useAuth()
  const { isSuperAdmin, isAdmin } = useRole()
  const { canViewProcedure, canEditProcedure } = usePermissions()

  const supabase = createClient()

  useEffect(() => {
    fetchProcedures()
    fetchProcedureTypes()
    fetchStatuses()
  }, [])

  const fetchProcedures = async () => {
    try {
      setLoading(true)
      let query = supabase
        .from('citizen_procedures')
        .select(`
          id,
          created_at,
          status_id,
          citizen_id,
          procedure_id,
          citizen:profiles!citizen_id(
            full_name,
            document_number,
            email
          ),
          procedure:procedures(
            name,
            cost,
            response_time_days,
            dependency:dependencies(name)
          ),
          status:procedure_statuses(
            id,
            name,
            display_name
          )
        `)

      // Filter based on user role
      if (isAdmin && profile?.dependency_id) {
        // Admin can only see procedures from their dependency
        query = query.in('procedure_id', 
          supabase
            .from('procedures')
            .select('id')
            .eq('dependency_id', profile.dependency_id)
        )
      }

      const { data, error } = await query.order('created_at', { ascending: false })

      if (error) throw error
      setProcedures(data || [])
    } catch (error) {
      console.error('Error fetching procedures:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchProcedureTypes = async () => {
    try {
      let query = supabase
        .from('procedures')
        .select('id, name, dependency:dependencies(name)')

      // Filter based on user role
      if (isAdmin && profile?.dependency_id) {
        query = query.eq('dependency_id', profile.dependency_id)
      }

      const { data, error } = await query.order('name')

      if (error) throw error
      setProcedureTypes(data || [])
    } catch (error) {
      console.error('Error fetching procedure types:', error)
    }
  }

  const fetchStatuses = async () => {
    try {
      const { data, error } = await supabase
        .from('procedure_statuses')
        .select('id, name, display_name')
        .order('name')

      if (error) throw error
      setStatuses(data || [])
    } catch (error) {
      console.error('Error fetching statuses:', error)
    }
  }

  const filteredProcedures = procedures.filter(procedure => {
    const matchesSearch = 
      procedure.citizen?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      procedure.citizen?.document_number?.includes(searchTerm) ||
      procedure.procedure?.name?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || procedure.status_id === statusFilter
    const matchesProcedureType = procedureTypeFilter === 'all' || procedure.procedure_id === procedureTypeFilter

    return matchesSearch && matchesStatus && matchesProcedureType
  })

  const handleViewProcedure = (procedure: CitizenProcedure) => {
    setSelectedProcedure(procedure)
    setIsDetailDialogOpen(true)
  }

  const handleUpdateStatus = async (procedureId: string, newStatusId: string) => {
    try {
      const { error } = await supabase
        .from('citizen_procedures')
        .update({ status_id: newStatusId })
        .eq('id', procedureId)

      if (error) throw error
      await fetchProcedures()
    } catch (error) {
      console.error('Error updating procedure status:', error)
    }
  }

  const getStatusBadgeVariant = (statusId: string) => {
    switch (statusId) {
      case 'pending': return 'secondary'
      case 'in_progress': return 'default'
      case 'completed': return 'default'
      case 'rejected': return 'destructive'
      case 'cancelled': return 'outline'
      default: return 'outline'
    }
  }

  const getStatusIcon = (statusId: string) => {
    switch (statusId) {
      case 'pending': return <Clock className="h-3 w-3" />
      case 'in_progress': return <AlertCircle className="h-3 w-3" />
      case 'completed': return <CheckCircle className="h-3 w-3" />
      case 'rejected': return <XCircle className="h-3 w-3" />
      case 'cancelled': return <XCircle className="h-3 w-3" />
      default: return <Clock className="h-3 w-3" />
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Gestión de Trámites</CardTitle>
              <CardDescription>
                <ConditionalContent
                  adminContent={`Administra trámites de ${profile?.dependency?.name || 'tu dependencia'}`}
                  superAdminContent="Administra todos los trámites del sistema"
                >
                  Gestiona los trámites ciudadanos
                </ConditionalContent>
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <Label htmlFor="search">Buscar</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Buscar por ciudadano, documento o trámite..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="w-full sm:w-48">
              <Label htmlFor="status-filter">Estado</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filtrar por estado" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los estados</SelectItem>
                  {statuses.map((status) => (
                    <SelectItem key={status.id} value={status.id}>
                      {status.display_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="w-full sm:w-48">
              <Label htmlFor="procedure-filter">Tipo de Trámite</Label>
              <Select value={procedureTypeFilter} onValueChange={setProcedureTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filtrar por trámite" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los trámites</SelectItem>
                  {procedureTypes.map((procedure) => (
                    <SelectItem key={procedure.id} value={procedure.id}>
                      {procedure.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Procedures Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Ciudadano</TableHead>
                  <TableHead>Trámite</TableHead>
                  <TableHead>Estado</TableHead>
                  <ShowForSuperAdmin>
                    <TableHead>Dependencia</TableHead>
                  </ShowForSuperAdmin>
                  <TableHead>Costo</TableHead>
                  <TableHead>Fecha</TableHead>
                  <TableHead className="text-right">Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      Cargando trámites...
                    </TableCell>
                  </TableRow>
                ) : filteredProcedures.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      No se encontraron trámites
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredProcedures.map((procedure) => (
                    <TableRow key={procedure.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{procedure.citizen?.full_name}</div>
                          <div className="text-sm text-gray-500">
                            CC: {procedure.citizen?.document_number}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 mr-2 text-gray-400" />
                          <div>
                            <div className="font-medium">{procedure.procedure?.name}</div>
                            <div className="text-sm text-gray-500">
                              {procedure.procedure?.response_time_days} días hábiles
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(procedure.status_id)}>
                          {getStatusIcon(procedure.status_id)}
                          <span className="ml-1">{procedure.status?.display_name}</span>
                        </Badge>
                      </TableCell>
                      <ShowForSuperAdmin>
                        <TableCell>
                          <div className="flex items-center text-sm">
                            <Building className="h-3 w-3 mr-1" />
                            {procedure.procedure?.dependency?.name}
                          </div>
                        </TableCell>
                      </ShowForSuperAdmin>
                      <TableCell>
                        <div className="flex items-center text-sm">
                          <DollarSign className="h-3 w-3 mr-1" />
                          {procedure.procedure?.cost ? 
                            `$${procedure.procedure.cost.toLocaleString('es-CO')}` : 
                            'Gratuito'
                          }
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-sm text-gray-500">
                          <Calendar className="h-3 w-3 mr-1" />
                          {new Date(procedure.created_at).toLocaleDateString('es-CO')}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <PermissionButton
                            requiredPermissions={['canViewProcedure']}
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewProcedure(procedure)}
                            resourceId={procedure.id}
                          >
                            <Eye className="h-3 w-3" />
                          </PermissionButton>
                          <PermissionButton
                            requiredPermissions={['canEditProcedure']}
                            variant="outline"
                            size="sm"
                            onClick={() => {/* TODO: Implement edit */}}
                            resourceId={procedure.id}
                          >
                            <Edit className="h-3 w-3" />
                          </PermissionButton>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Procedure Detail Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Detalle del Trámite</DialogTitle>
            <DialogDescription>
              Información completa del trámite seleccionado
            </DialogDescription>
          </DialogHeader>
          {selectedProcedure && (
            <ProcedureDetailView
              procedure={selectedProcedure}
              statuses={statuses}
              onStatusUpdate={handleUpdateStatus}
              onClose={() => setIsDetailDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Procedure Detail View Component
interface ProcedureDetailViewProps {
  procedure: CitizenProcedure
  statuses: any[]
  onStatusUpdate: (procedureId: string, statusId: string) => void
  onClose: () => void
}

function ProcedureDetailView({ procedure, statuses, onStatusUpdate, onClose }: ProcedureDetailViewProps) {
  const [newStatus, setNewStatus] = useState(procedure.status_id)
  const { canEditProcedure } = usePermissions()

  const handleStatusUpdate = () => {
    if (newStatus !== procedure.status_id) {
      onStatusUpdate(procedure.id, newStatus)
      onClose()
    }
  }

  return (
    <div className="space-y-6">
      {/* Citizen Information */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label className="text-sm font-medium text-gray-500">Ciudadano</Label>
          <p className="text-sm">{procedure.citizen?.full_name}</p>
        </div>
        <div>
          <Label className="text-sm font-medium text-gray-500">Documento</Label>
          <p className="text-sm">{procedure.citizen?.document_number}</p>
        </div>
        <div>
          <Label className="text-sm font-medium text-gray-500">Email</Label>
          <p className="text-sm">{procedure.citizen?.email}</p>
        </div>
        <div>
          <Label className="text-sm font-medium text-gray-500">Fecha de Solicitud</Label>
          <p className="text-sm">{new Date(procedure.created_at).toLocaleDateString('es-CO')}</p>
        </div>
      </div>

      {/* Procedure Information */}
      <div className="border-t pt-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label className="text-sm font-medium text-gray-500">Trámite</Label>
            <p className="text-sm">{procedure.procedure?.name}</p>
          </div>
          <div>
            <Label className="text-sm font-medium text-gray-500">Dependencia</Label>
            <p className="text-sm">{procedure.procedure?.dependency?.name}</p>
          </div>
          <div>
            <Label className="text-sm font-medium text-gray-500">Costo</Label>
            <p className="text-sm">
              {procedure.procedure?.cost ? 
                `$${procedure.procedure.cost.toLocaleString('es-CO')}` : 
                'Gratuito'
              }
            </p>
          </div>
          <div>
            <Label className="text-sm font-medium text-gray-500">Tiempo de Respuesta</Label>
            <p className="text-sm">{procedure.procedure?.response_time_days} días hábiles</p>
          </div>
        </div>
      </div>

      {/* Status Update */}
      {canEditProcedure(procedure.id) && (
        <div className="border-t pt-4">
          <Label htmlFor="status">Actualizar Estado</Label>
          <div className="flex gap-2 mt-2">
            <Select value={newStatus} onValueChange={setNewStatus}>
              <SelectTrigger className="flex-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {statuses.map((status) => (
                  <SelectItem key={status.id} value={status.id}>
                    {status.display_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button 
              onClick={handleStatusUpdate}
              disabled={newStatus === procedure.status_id}
            >
              Actualizar
            </Button>
          </div>
        </div>
      )}

      <DialogFooter>
        <Button variant="outline" onClick={onClose}>
          Cerrar
        </Button>
      </DialogFooter>
    </div>
  )
}
