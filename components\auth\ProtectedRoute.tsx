'use client'

import { ReactNode } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { useRole } from '@/hooks/useRole'
import { usePermissions } from '@/hooks/usePermissions'
import { Loader2, AlertTriangle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'

type RoleName = 'ciudadano' | 'admin' | 'super_admin'

interface ProtectedRouteProps {
  children: ReactNode
  requiredRoles?: RoleName[]
  requiredPermissions?: string[]
  fallbackPath?: string
  showFallback?: boolean
  loadingComponent?: ReactNode
  unauthorizedComponent?: ReactNode
}

export function ProtectedRoute({
  children,
  requiredRoles = [],
  requiredPermissions = [],
  fallbackPath = '/auth/login',
  showFallback = true,
  loadingComponent,
  unauthorizedComponent,
}: ProtectedRouteProps) {
  const router = useRouter()
  const { isAuthenticated, isLoading } = useAuth()
  const { hasAnyRole, roleName } = useRole()
  const permissions = usePermissions()

  // Show loading state
  if (isLoading) {
    if (loadingComponent) {
      return <>{loadingComponent}</>
    }

    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <Loader2 className="mx-auto h-8 w-8 animate-spin text-chia-blue-600" />
          <p className="text-gray-600">Verificando permisos...</p>
        </div>
      </div>
    )
  }

  // Check authentication
  if (!isAuthenticated) {
    if (showFallback) {
      router.push(fallbackPath)
      return null
    }

    if (unauthorizedComponent) {
      return <>{unauthorizedComponent}</>
    }

    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="max-w-md w-full space-y-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Debes iniciar sesión para acceder a esta página.
            </AlertDescription>
          </Alert>
          <Button
            onClick={() => router.push(fallbackPath)}
            className="w-full bg-chia-blue-600 hover:bg-chia-blue-700"
          >
            Iniciar Sesión
          </Button>
        </div>
      </div>
    )
  }

  // Check role requirements
  if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {
    if (unauthorizedComponent) {
      return <>{unauthorizedComponent}</>
    }

    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="max-w-md w-full space-y-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              No tienes permisos suficientes para acceder a esta página.
              <br />
              <span className="text-sm text-gray-600 mt-2 block">
                Rol requerido: {requiredRoles.join(', ')}
                <br />
                Tu rol actual: {roleName || 'Sin rol'}
              </span>
            </AlertDescription>
          </Alert>
          <Button
            onClick={() => router.push('/dashboard')}
            className="w-full bg-chia-blue-600 hover:bg-chia-blue-700"
          >
            Ir al Dashboard
          </Button>
        </div>
      </div>
    )
  }

  // Check permission requirements
  if (requiredPermissions.length > 0) {
    const hasAllPermissions = requiredPermissions.every(permission => {
      // Check if the permission method exists in permissions object
      const permissionMethod = permissions[permission as keyof typeof permissions]
      if (typeof permissionMethod === 'function') {
        return permissionMethod()
      }
      return false
    })

    if (!hasAllPermissions) {
      if (unauthorizedComponent) {
        return <>{unauthorizedComponent}</>
      }

      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="max-w-md w-full space-y-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                No tienes los permisos necesarios para acceder a esta funcionalidad.
                <br />
                <span className="text-sm text-gray-600 mt-2 block">
                  Permisos requeridos: {requiredPermissions.join(', ')}
                </span>
              </AlertDescription>
            </Alert>
            <Button
              onClick={() => router.push('/dashboard')}
              className="w-full bg-chia-blue-600 hover:bg-chia-blue-700"
            >
              Ir al Dashboard
            </Button>
          </div>
        </div>
      )
    }
  }

  // All checks passed, render children
  return <>{children}</>
}

// Convenience components for common use cases
export function AdminRoute({ children, ...props }: Omit<ProtectedRouteProps, 'requiredRoles'>) {
  return (
    <ProtectedRoute requiredRoles={['admin', 'super_admin']} {...props}>
      {children}
    </ProtectedRoute>
  )
}

export function SuperAdminRoute({ children, ...props }: Omit<ProtectedRouteProps, 'requiredRoles'>) {
  return (
    <ProtectedRoute requiredRoles={['super_admin']} {...props}>
      {children}
    </ProtectedRoute>
  )
}

export function CitizenRoute({ children, ...props }: Omit<ProtectedRouteProps, 'requiredRoles'>) {
  return (
    <ProtectedRoute requiredRoles={['ciudadano', 'admin', 'super_admin']} {...props}>
      {children}
    </ProtectedRoute>
  )
}
