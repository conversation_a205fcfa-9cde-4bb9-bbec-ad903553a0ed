'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { 
  Search, 
  Building, 
  Plus, 
  Edit, 
  Trash2, 
  Users,
  FileText,
  Calendar,
  MapPin
} from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { useAuth, usePermissions } from '@/hooks'
import { PermissionButton } from '@/components/auth'

interface Dependency {
  id: string
  name: string
  description: string
  address: string
  phone: string
  email: string
  created_at: string
  _count?: {
    users: number
    procedures: number
  }
}

export function DependencyManagement() {
  const [dependencies, setDependencies] = useState<Dependency[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedDependency, setSelectedDependency] = useState<Dependency | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  const { canManageDependencies } = usePermissions()
  const supabase = createClient()

  useEffect(() => {
    fetchDependencies()
  }, [])

  const fetchDependencies = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('dependencies')
        .select(`
          id,
          name,
          description,
          address,
          phone,
          email,
          created_at
        `)
        .order('name')

      if (error) throw error

      // Get counts for each dependency
      const dependenciesWithCounts = await Promise.all(
        (data || []).map(async (dependency) => {
          const [
            { count: userCount },
            { count: procedureCount }
          ] = await Promise.all([
            supabase
              .from('profiles')
              .select('*', { count: 'exact', head: true })
              .eq('dependency_id', dependency.id),
            supabase
              .from('procedures')
              .select('*', { count: 'exact', head: true })
              .eq('dependency_id', dependency.id)
          ])

          return {
            ...dependency,
            _count: {
              users: userCount || 0,
              procedures: procedureCount || 0
            }
          }
        })
      )

      setDependencies(dependenciesWithCounts)
    } catch (error) {
      console.error('Error fetching dependencies:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredDependencies = dependencies.filter(dependency =>
    dependency.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dependency.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleCreateDependency = async (data: any) => {
    try {
      const { error } = await supabase
        .from('dependencies')
        .insert([data])

      if (error) throw error
      await fetchDependencies()
      setIsCreateDialogOpen(false)
    } catch (error) {
      console.error('Error creating dependency:', error)
    }
  }

  const handleEditDependency = (dependency: Dependency) => {
    setSelectedDependency(dependency)
    setIsEditDialogOpen(true)
  }

  const handleUpdateDependency = async (data: any) => {
    if (!selectedDependency) return

    try {
      const { error } = await supabase
        .from('dependencies')
        .update(data)
        .eq('id', selectedDependency.id)

      if (error) throw error
      await fetchDependencies()
      setIsEditDialogOpen(false)
      setSelectedDependency(null)
    } catch (error) {
      console.error('Error updating dependency:', error)
    }
  }

  const handleDeleteDependency = async (dependencyId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar esta dependencia? Esta acción no se puede deshacer.')) return

    try {
      const { error } = await supabase
        .from('dependencies')
        .delete()
        .eq('id', dependencyId)

      if (error) throw error
      await fetchDependencies()
    } catch (error) {
      console.error('Error deleting dependency:', error)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Gestión de Dependencias</CardTitle>
              <CardDescription>
                Administra las dependencias municipales del sistema
              </CardDescription>
            </div>
            <PermissionButton
              requiredPermissions={['canManageDependencies']}
              variant="default"
              onClick={() => setIsCreateDialogOpen(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Nueva Dependencia
            </PermissionButton>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search */}
          <div className="mb-6">
            <Label htmlFor="search">Buscar</Label>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="search"
                placeholder="Buscar por nombre o descripción..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Dependencies Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Dependencia</TableHead>
                  <TableHead>Contacto</TableHead>
                  <TableHead>Estadísticas</TableHead>
                  <TableHead>Creación</TableHead>
                  <TableHead className="text-right">Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      Cargando dependencias...
                    </TableCell>
                  </TableRow>
                ) : filteredDependencies.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      No se encontraron dependencias
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredDependencies.map((dependency) => (
                    <TableRow key={dependency.id}>
                      <TableCell>
                        <div>
                          <div className="flex items-center">
                            <Building className="h-4 w-4 mr-2 text-gray-400" />
                            <span className="font-medium">{dependency.name}</span>
                          </div>
                          {dependency.description && (
                            <p className="text-sm text-gray-500 mt-1">
                              {dependency.description}
                            </p>
                          )}
                          {dependency.address && (
                            <div className="flex items-center text-sm text-gray-500 mt-1">
                              <MapPin className="h-3 w-3 mr-1" />
                              {dependency.address}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {dependency.email && (
                            <div className="text-sm">{dependency.email}</div>
                          )}
                          {dependency.phone && (
                            <div className="text-sm text-gray-500">{dependency.phone}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center text-sm">
                            <Users className="h-3 w-3 mr-1" />
                            <span>{dependency._count?.users || 0} usuarios</span>
                          </div>
                          <div className="flex items-center text-sm">
                            <FileText className="h-3 w-3 mr-1" />
                            <span>{dependency._count?.procedures || 0} trámites</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-sm text-gray-500">
                          <Calendar className="h-3 w-3 mr-1" />
                          {new Date(dependency.created_at).toLocaleDateString('es-CO')}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <PermissionButton
                            requiredPermissions={['canManageDependencies']}
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditDependency(dependency)}
                          >
                            <Edit className="h-3 w-3" />
                          </PermissionButton>
                          <PermissionButton
                            requiredPermissions={['canManageDependencies']}
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteDependency(dependency.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </PermissionButton>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Create Dependency Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Nueva Dependencia</DialogTitle>
            <DialogDescription>
              Crea una nueva dependencia municipal en el sistema.
            </DialogDescription>
          </DialogHeader>
          <DependencyForm
            onSave={handleCreateDependency}
            onCancel={() => setIsCreateDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Dependency Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Editar Dependencia</DialogTitle>
            <DialogDescription>
              Modifica la información de la dependencia seleccionada.
            </DialogDescription>
          </DialogHeader>
          {selectedDependency && (
            <DependencyForm
              dependency={selectedDependency}
              onSave={handleUpdateDependency}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Dependency Form Component
interface DependencyFormProps {
  dependency?: Dependency
  onSave: (data: any) => void
  onCancel: () => void
}

function DependencyForm({ dependency, onSave, onCancel }: DependencyFormProps) {
  const [formData, setFormData] = useState({
    name: dependency?.name || '',
    description: dependency?.description || '',
    address: dependency?.address || '',
    phone: dependency?.phone || '',
    email: dependency?.email || ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave(formData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="name">Nombre *</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
        />
      </div>

      <div>
        <Label htmlFor="description">Descripción</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          rows={3}
        />
      </div>

      <div>
        <Label htmlFor="address">Dirección</Label>
        <Input
          id="address"
          value={formData.address}
          onChange={(e) => setFormData({ ...formData, address: e.target.value })}
        />
      </div>

      <div>
        <Label htmlFor="phone">Teléfono</Label>
        <Input
          id="phone"
          value={formData.phone}
          onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
        />
      </div>

      <div>
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
        />
      </div>

      <DialogFooter>
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancelar
        </Button>
        <Button type="submit">
          {dependency ? 'Actualizar' : 'Crear'} Dependencia
        </Button>
      </DialogFooter>
    </form>
  )
}
