'use client'

import { useState, useRef } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  X,
  Upload,
  File,
  FileText,
  Image,
  Download,
  Trash2,
  AlertCircle,
  CheckCircle,
  Eye,
  Plus,
  Paperclip
} from 'lucide-react'

interface DocumentUploadModalProps {
  procedure: any
  onClose: () => void
}

interface UploadedFile {
  id: string
  name: string
  size: number
  type: string
  url: string
  uploadedAt: string
}

export function DocumentUploadModal({
  procedure,
  onClose
}: DocumentUploadModalProps) {
  const [files, setFiles] = useState<UploadedFile[]>([])
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [dragActive, setDragActive] = useState(false)
  const [error, setError] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)

  const supabase = createClient()

  // Load existing files on component mount
  useState(() => {
    loadExistingFiles()
  })

  const loadExistingFiles = async () => {
    try {
      if (procedure.attachments && procedure.attachments.length > 0) {
        // Load file details from storage
        const filePromises = procedure.attachments.map(async (filePath: string) => {
          const { data } = await supabase.storage
            .from('documents')
            .getPublicUrl(filePath)
          
          return {
            id: filePath,
            name: filePath.split('/').pop() || 'Unknown',
            size: 0, // We don't have size info from existing attachments
            type: 'application/octet-stream',
            url: data.publicUrl,
            uploadedAt: procedure.updated_at
          }
        })

        const existingFiles = await Promise.all(filePromises)
        setFiles(existingFiles)
      }
    } catch (error) {
      console.error('Error loading existing files:', error)
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(Array.from(e.dataTransfer.files))
    }
  }

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(Array.from(e.target.files))
    }
  }

  const handleFiles = async (fileList: File[]) => {
    setError('')
    setUploading(true)
    setUploadProgress(0)

    try {
      const uploadPromises = fileList.map(async (file, index) => {
        // Validate file
        if (file.size > 10 * 1024 * 1024) { // 10MB limit
          throw new Error(`El archivo ${file.name} es demasiado grande (máximo 10MB)`)
        }

        // Generate unique file path
        const fileExt = file.name.split('.').pop()
        const fileName = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}.${fileExt}`
        const filePath = `${procedure.citizen_id}/${procedure.id}/${fileName}`

        // Upload file
        const { data, error } = await supabase.storage
          .from('documents')
          .upload(filePath, file)

        if (error) throw error

        // Update progress
        setUploadProgress(((index + 1) / fileList.length) * 100)

        // Get public URL
        const { data: urlData } = supabase.storage
          .from('documents')
          .getPublicUrl(filePath)

        return {
          id: filePath,
          name: file.name,
          size: file.size,
          type: file.type,
          url: urlData.publicUrl,
          uploadedAt: new Date().toISOString()
        }
      })

      const uploadedFiles = await Promise.all(uploadPromises)
      
      // Update files state
      setFiles(prev => [...prev, ...uploadedFiles])

      // Update procedure attachments in database
      const allFilePaths = [...(procedure.attachments || []), ...uploadedFiles.map(f => f.id)]
      
      const { error: updateError } = await supabase
        .from('citizen_procedures')
        .update({ 
          attachments: allFilePaths,
          updated_at: new Date().toISOString()
        })
        .eq('id', procedure.id)

      if (updateError) throw updateError

      // Create notification
      await supabase
        .from('notifications')
        .insert({
          user_id: procedure.citizen_id,
          title: 'Documentos subidos',
          message: `Se han subido ${uploadedFiles.length} documento(s) al trámite ${procedure.reference_number}`,
          type: 'document_uploaded',
          related_id: procedure.id,
          is_read: false
        })

    } catch (err: any) {
      setError(err.message || 'Error al subir archivos')
    } finally {
      setUploading(false)
      setUploadProgress(0)
    }
  }

  const handleDeleteFile = async (fileId: string) => {
    try {
      // Delete from storage
      const { error: deleteError } = await supabase.storage
        .from('documents')
        .remove([fileId])

      if (deleteError) throw deleteError

      // Update files state
      setFiles(prev => prev.filter(f => f.id !== fileId))

      // Update procedure attachments
      const updatedAttachments = files
        .filter(f => f.id !== fileId)
        .map(f => f.id)

      const { error: updateError } = await supabase
        .from('citizen_procedures')
        .update({ 
          attachments: updatedAttachments,
          updated_at: new Date().toISOString()
        })
        .eq('id', procedure.id)

      if (updateError) throw updateError

    } catch (err: any) {
      setError(err.message || 'Error al eliminar archivo')
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return <Image className="h-5 w-5" />
    if (type.includes('pdf')) return <FileText className="h-5 w-5" />
    return <File className="h-5 w-5" />
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Gestión de Documentos
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              {procedure.procedure.name} - {procedure.reference_number}
            </p>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="space-y-6">
            {/* Required Documents */}
            {procedure.procedure.documents_required && procedure.procedure.documents_required.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <AlertCircle className="h-5 w-5 mr-2 text-orange-600" />
                    Documentos Requeridos
                  </CardTitle>
                  <CardDescription>
                    Asegúrate de subir todos los documentos necesarios para tu trámite
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {procedure.procedure.documents_required.map((doc: string, index: number) => (
                      <li key={index} className="flex items-start">
                        <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-700">{doc}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Upload Area */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Upload className="h-5 w-5 mr-2 text-blue-600" />
                  Subir Documentos
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div
                  className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                    dragActive 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-lg font-medium text-gray-900 mb-2">
                    Arrastra archivos aquí o haz clic para seleccionar
                  </p>
                  <p className="text-sm text-gray-500 mb-4">
                    Formatos soportados: PDF, DOC, DOCX, JPG, PNG (máximo 10MB por archivo)
                  </p>
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={uploading}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Seleccionar Archivos
                  </Button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    onChange={handleFileInput}
                    className="hidden"
                    aria-label="Seleccionar archivos para subir"
                  />
                </div>

                {uploading && (
                  <div className="mt-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600">Subiendo archivos...</span>
                      <span className="text-sm text-gray-600">{Math.round(uploadProgress)}%</span>
                    </div>
                    <Progress value={uploadProgress} className="w-full" />
                  </div>
                )}

                {error && (
                  <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="flex">
                      <AlertCircle className="h-5 w-5 text-red-400 mr-2" />
                      <p className="text-sm text-red-700">{error}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Uploaded Files */}
            {files.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Paperclip className="h-5 w-5 mr-2 text-gray-600" />
                    Documentos Subidos ({files.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {files.map((file) => (
                      <div
                        key={file.id}
                        className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="text-gray-500">
                            {getFileIcon(file.type)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {file.name}
                            </p>
                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              <span>{formatFileSize(file.size)}</span>
                              <span>
                                Subido: {new Date(file.uploadedAt).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(file.url, '_blank')}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              const link = document.createElement('a')
                              link.href = file.url
                              link.download = file.name
                              link.click()
                            }}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteFile(file.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Instructions */}
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="p-4">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
                  <div className="text-sm text-blue-800">
                    <p className="font-medium mb-1">Instrucciones importantes:</p>
                    <ul className="space-y-1 text-blue-700">
                      <li>• Los documentos deben estar en formato PDF, DOC, DOCX, JPG o PNG</li>
                      <li>• El tamaño máximo por archivo es de 10MB</li>
                      <li>• Asegúrate de que los documentos sean legibles y estén completos</li>
                      <li>• Una vez subidos, los documentos serán revisados por el personal correspondiente</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t bg-gray-50">
          <div className="text-sm text-gray-600">
            {files.length > 0 ? (
              <span className="flex items-center">
                <CheckCircle className="h-4 w-4 text-green-600 mr-1" />
                {files.length} documento(s) subido(s)
              </span>
            ) : (
              <span>No hay documentos subidos</span>
            )}
          </div>
          <Button onClick={onClose}>
            Cerrar
          </Button>
        </div>
      </div>
    </div>
  )
}
